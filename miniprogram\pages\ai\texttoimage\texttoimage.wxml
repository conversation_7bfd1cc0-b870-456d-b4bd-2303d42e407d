<wxs src="../../../utils/constants.wxs" module="constants" />
<image class="bg-image" src="{{constants.COMMON_ASSETS.BG}}" mode="aspectFill"></image>
<view class="container">
  <nav-bar title="AI艺术图像生成" showBack="{{true}}" showMore=""><!-- 顶部导航 --></nav-bar>
  <view class="content-wrapper" style="{{isTabBarCollapsed?layoutStyle_cropper:layoutStyle}}">
    <scroll-view scroll-y class="content-scroll {{showCropper ? 'no-scroll' : ''}}" enhanced="{{true}}" bounces="{{true}}" scroll-top="{{scrollTop}}">
      <!-- 内容区域开始 -->
        <view wx:if="{{generating}}" class="progress-container">
          <!-- 进度条已移除，改为loading动画 -->
          <view class="custom-loading"></view>
          <view class="progress-text">{{progressText}}</view>
          <!-- 队列信息显示区域 -->
          <view wx:if="{{queuePosition !== null && totalQueued !== null}}" class="progress-text">
            <block wx:if="{{queuePosition > 0}}">排队中 {{queuePosition}}/{{totalQueued}}</block>
            <block wx:elif="{{queuePosition === 0}}">正在生成</block>
          </view>
          <view wx:if="{{queuePosition !== null && totalQueued !== null}}" class="progress-text">
            <block wx:if="{{queuePosition === 0}}">预计等待16秒</block>
            <block wx:elif="{{queuePosition <= 1}}">预计等待{{queuePosition*48}}秒</block>
            <block wx:elif="{{queuePosition > 1}}">预计等待{{queuePosition*15}}秒</block>
          </view>
        </view>
        <view wx:if="{{generatedImageResult && !ImageError}}" class="result-section">
          <view class="result-title">生成结果</view>
          <image
            src="{{generatedImageResult.imagePath ? generatedImageResult.imagePath : '/assets/placeholder.png'}}"
            mode="widthFix"
            class="generated-image"
            binderror="handleImageError"
            bindtap="previewImage"
          ></image>
          <view class="image-actions" style="display:none;">
            <button class="action-btn save-btn" bindtap="saveImage">保存到相册</button>
            <button class="action-btn preview-btn" bindtap="previewImage">预览图片</button>
          </view>
        </view>
        <view wx:if="{{ImageError}}" class="result-section">
          <view class="result-title">抱歉抱歉哦~</view>
          <view class="error-message">{{ImageErrortitle}}</view>
        </view>
      <view class="texttoimage-container">
        <view class="prompt-input-container">
          <textarea 
            class="prompt-input" 
            placeholder="请输入图片描述（如：一只橙色的猫坐在草地上，阳光明媚的日子）" 
            value="{{prompt}}"
            bindinput="onPromptInput"
            maxlength="2000"
            auto-height
          ></textarea>
          <view class="clear-prompt-btn" bindtap="clearPrompt" wx:if="{{prompt.length > 0}}">清 空</view>
          <view class="prompt-length">{{prompt.length}}/2000</view>
        </view>
        
        <!-- 提示词工具按钮 -->
        <view class="prompt-tools">
          
          <button class="tool-btn expand-btn" bindtap="handleExpandPrompt" disabled="{{buttonCooldowns.expand.disabled}}">
            <text class="tool-btn-text">
              <block wx:if="{{buttonCooldowns.expand.cooldown > 0}}">{{buttonCooldowns.expand.cooldown}}秒后可用</block>
              <block wx:else>提示词扩充</block>
            </text>
          </button>
          <button class="tool-btn random-btn" bindtap="handleRandomPrompt">
            <text class="tool-btn-text">🎲 随机提示词</text>
          </button>
          <!-- <button class="tool-btn translate-btn" bindtap="handleTranslate" disabled="{{buttonCooldowns.translate.disabled}}">
            <text class="tool-btn-text">
              <block wx:if="{{buttonCooldowns.translate.cooldown > 0}}">{{buttonCooldowns.translate.cooldown}}秒后可用</block>
              <block wx:else>翻译成英文</block>
            </text>
          </button> -->
          <button class="tool-btn settings-btn" bindtap="toggleSettings">
            <!-- <text class="tool-btn-icon">⚙️</text> -->
            <text class="tool-btn-text">{{showSettings ? '收起设置' : '生成设置'}}</text>
            <text class="settings-arrow">{{showSettings ? '▲' : '▼'}}</text>
          </button>
        </view>
        <view wx:if="{{showSettings}}" class="settings-panel">
          <view class="api-selector">
            <view class="selector-title">生成效果</view>
            <view class="model-and-subject-container flex-row-simple">
              <view wx:if="{{Pollinations_modelList}}" class="pollinations-model-picker selector-box">
                <picker mode="selector" range="{{pollinationsModels}}" value="{{selectedPollinationsModelIndex}}" bindchange="onPollinationsModelChange">
                  <view class="picker-inner selector-box" style="border:none;">
                    <text>模型：</text>
                    <text class="model-name">{{pollinationsModels[selectedPollinationsModelIndex] || '无可用模型'}}</text>
                  </view>
                </picker>
              </view>
              <view class="keep-subject-switch selector-box" style="margin-top: 20rpx;">
                <switch checked="{{keepSubject}}" bindchange="onKeepSubjectChange" color="#4CAF50" style="margin-right: 8rpx;" />
                <text class="subject-label">保持主体</text>
              </view>
            </view>
          </view>
          <view class="size-selector">
            <view class="selector-title">选择图片比例</view>
            <scroll-view class="size-scroll" scroll-x enable-flex show-scrollbar="{{false}}">
              <view 
                class="size-option {{selectedSizeIndex === index ? 'selected' : ''}}" 
                wx:for="{{imageSizes}}" 
                wx:key="value"
                data-index="{{index}}"
                bindtap="selectImageSize"
              >
                <view class="size-icon {{item.icon}}"></view>
                <view class="size-label">{{item.label}}</view>
              </view>
              <view class="scroll-spacer"></view>
            </scroll-view>
          </view>
          <view class="style-selector">
            <view class="selector-title">选择画面风格</view>
            <scroll-view class="style-scroll" scroll-x enable-flex show-scrollbar="{{false}}">
              <view 
                class="style-option {{selectedStyleIndex === index ? 'selected' : ''}}" 
                wx:for="{{imageStyles}}" 
                wx:key="label"
                data-index="{{index}}"
                bindtap="selectImageStyle"
              >
                <view class="style-img-container">
                  <image 
                    src="{{constants.DOMAIN+item.img}}" 
                    mode="aspectFill" 
                    class="style-image"
                  ></image>
                  <view class="style-label-container">
                    <text class="style-label">{{item.label}}</text>
                  </view>
                </view>
              </view>
              <view class="scroll-spacer"></view>
            </scroll-view>
          </view>
        </view>
        
        <view class="button-container">
          <!-- 生成按钮：根据showGenerateBtn显示 -->
          <button wx:if="{{showGenerateBtn}}" class="generate-btn" bindtap="generateImage" disabled="{{generating || buttonCooldowns.generate.disabled}}">
            <block wx:if="{{generating}}">生成中...</block>
            <block wx:elif="{{buttonCooldowns.generate.cooldown > 0}}">冷却中{{buttonCooldowns.generate.cooldown}}秒</block>
            <block wx:else>生成图片</block>
          </button>
          <!-- 分享按钮：根据showShareBtn显示 -->
          <button wx:if="{{showShareBtn}}" class="share-btn" open-type="share" bindtap="handleShare">{{shareBtnText}}</button>
          <!-- 使用次数提示 -->
          <view class="usage-info">
            <text class="usage-count"><text wx:if="{{!showShareBtn}}">已使用{{generateCount}}/{{currentShareNumber}}次  </text><text class="increase-tag" bindtap="toggleIncreaseTip">增加上限</text></text>
            <view wx:if="{{showIncreaseTip}}" class="increase-tip-detail" style="width: 80%; padding-left: 50rpx;">
              <view class="increase-tip-item">1. 生成全部免费，没有总数限制！</view>
              <view class="increase-tip-item">2. {{showShareBtn?'分享成功即可翻倍增加当日生图数量':'达到上限分享给好友即可继续使用'}}！</view>
              <view class="increase-tip-item">3. 上限计算：默认 {{shareNumber}} 张 * {{shareTimes ? ('已分享 ' + shareTimes + ' 次') : '分享次数'}}。</view>
              <view class="increase-tip-item">4. 当前上限为 {{currentShareNumber}} 次，将于 0 点自动重置。</view>
            </view>
          </view>
        </view>
        
      </view>
      
      <!-- FEED远程图片区 -->
      <view class="feed-section">
        <view class="section-title-row">
          <view class="section-title">其他小伙伴的创作</view>
          <view class="feed-actions">
            <view class="refresh-feed-btn" bindtap="onRefreshFeedTap">
              <text class="refresh-icon">↻</text>
              <text>刷新</text>
            </view>
            <view class="my-assets-link" bindtap="goToMyAssets">
              <text>我的资产</text>
            </view>
          </view>
        </view>
        <view wx:if="{{feedLoading}}" class="feed-loading">loading...</view>
        <view wx:elif="{{feedList.length === 0}}" class="feed-empty">暂无Feed数据</view>
        <view wx:else class="feed-grid">
          <view class="feed-item" wx:for="{{currentFeedPageList}}" wx:key="imageURL">
            <view class="feed-image-wrapper">
              <image class="feed-image" src="{{item.displayImageURL ? item.displayImageURL : item.imageURL}}" mode="aspectFill" data-url="{{item.imageURL}}" bindtap="showFeedImageModal" bindload="onFeedImageLoad" binderror="onFeedImageError" data-index="{{index}}" />
              <view class="feed-image-loading" wx:if="{{!feedImageLoaded[index]}}">图片加载中...</view>
            </view>
            <!-- <view class="feed-meta">
              <view class="feed-info">模型:{{item.model}} 种子:{{item.seed}}</view>
            </view> -->
          </view>
        </view>
        <!-- 分页控件 -->
        <view class="feed-pagination">
          <button class="pagination-btn" size="mini" bindtap="loadPrevFeedPage" disabled="{{isPrevDisabled}}">上一页</button>
          <text class="pagination-info">{{feedPage}}/{{totalPages}} (前{{feedTotal}}条)</text>
          <button class="pagination-btn" size="mini" bindtap="loadNextFeedPage" disabled="{{isNextDisabled}}">下一页</button>
        </view>
      </view>
      <!-- 说明开始 -->
      <view class="tool-intro">
        <view class="intro-header">
          <view class="intro-icon">
            <image src="{{constants.STATIC_URL.ICON}}picture.svg" mode="aspectFit"></image>
          </view>
          <text class="intro-title">AI文生图</text>
        </view>

        <view class="intro-content">
          <view class="feature-section">
            <text class="section-title">主要功能</text>
            <view class="feature-list">
              <view class="feature-item">
                <view class="feature-icon">
                  <text class="text">✦</text>
                </view>
                <view class="feature-content">
                  <text class="feature-name">文本到图像</text>
                  <text class="feature-desc">通过文字描述生成对应的图像</text>
                </view>
              </view>
              <view class="feature-item">
                <view class="feature-icon">
                  <text class="text">✦</text>
                </view>
                <view class="feature-content">
                  <text class="feature-name">多种比例</text>
                  <text class="feature-desc">支持多种图片尺寸比例</text>
                </view>
              </view>
              <view class="feature-item">
                <view class="feature-icon">
                  <text class="text">✦</text>
                </view>
                <view class="feature-content">
                  <text class="feature-name">高清图像</text>
                  <text class="feature-desc">生成高清晰度的AI图像</text>
                </view>
              </view>
              <view class="feature-item">
                <view class="feature-icon">
                  <text class="text">✦</text>
                </view>
                <view class="feature-content">
                  <text class="feature-name">一键保存</text>
                  <text class="feature-desc">快速保存生成的图片到相册</text>
                </view>
              </view>
            </view>
          </view>

          <view class="usage-section">
            <text class="section-title">使用步骤</text>
            <view class="step-list">
              <view class="step-item">
                <view class="step-number">1</view>
                <view class="step-content">
                  <text class="step-title">输入描述</text>
                  <text class="step-desc">详细描述你想要生成的图像内容</text>
                </view>
              </view>
              <view class="step-item">
                <view class="step-number">2</view>
                <view class="step-content">
                  <text class="step-title">选择比例</text>
                  <text class="step-desc">选择适合你需求的图片比例</text>
                </view>
              </view>
              <view class="step-item">
                <view class="step-number">3</view>
                <view class="step-content">
                  <text class="step-title">生成图片</text>
                  <text class="step-desc">点击生成按钮，等待AI创作完成</text>
                </view>
              </view>
              <view class="step-item">
                <view class="step-number">4</view>
                <view class="step-content">
                  <text class="step-title">保存分享</text>
                  <text class="step-desc">保存图片到相册或分享给朋友</text>
                </view>
              </view>
            </view>
          </view>

          <view class="tip-section">
            <text class="section-title">使用技巧</text>
            <view class="tip-list">
              <view class="tip-item">
                <view class="tip-icon">💡</view>
                <text class="tip-title">详细描述</text>
                <text class="tip-text">越详细的描述越能得到符合预期的图像</text>
              </view>
              <view class="tip-item">
                <view class="tip-icon">💡</view>
                <text class="tip-title">场景描述</text>
                <text class="tip-text">添加场景、光线、氛围等元素获得更好效果</text>
              </view>
              <view class="tip-item">
                <view class="tip-icon">💡</view>
                <text class="tip-title">风格词汇</text>
                <text class="tip-text">添加"油画风格"、"写实风格"等风格词</text>
              </view>
              <view class="tip-item">
                <view class="tip-icon">💡</view>
                <text class="tip-title">选择合适比例</text>
                <text class="tip-text">根据内容选择合适的图片比例</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- 说明结束 -->
      <!-- 内容区域结束 -->

      <!-- 交互禁用层 -->
      <view wx:if="{{generating || loading}}" class="interaction-disabled-overlay"></view>

      

    </scroll-view>
  </view>
  <tab-bar height="{{tabBarHeight}}" currentTab="0"><!-- 底部导航 --></tab-bar>
</view>

<!-- 添加wxs工具函数，用于格式化日期 -->
<wxs module="tools">
  function formatDate(timestamp) {
    var date = getDate(timestamp);
    var year = date.getFullYear();
    var month = date.getMonth() + 1;
    var day = date.getDate();
    var hour = date.getHours();
    var minute = date.getMinutes();
    
    month = month < 10 ? '0' + month : month;
    day = day < 10 ? '0' + day : day;
    hour = hour < 10 ? '0' + hour : hour;
    minute = minute < 10 ? '0' + minute : minute;
    
    return year + '-' + month + '-' + day + ' ' + hour + ':' + minute;
  }
  
  // 将尺寸转换为比例格式
  // 例如：将"1024x1024"转换为"1:1"
  function convertSizeToRatio(size) {
    if (!size) return '';
    
    // 根据尺寸返回对应的比例
    var sizeMap = {
      '1024x1024': '1:1',
      '720x1440': '1:2',
      '768x1344': '9:16',
      '864x1152': '3:4',
      '1344x768': '16:9',
      '1152x864': '4:3',
      '1440x720': '2:1'
    };
    
    // 如果找到匹配的比例，返回它
    if (sizeMap[size]) {
      return sizeMap[size];
    }
    
    // 如果没有匹配的，返回原始尺寸
    return size;
  }
  
  module.exports = {
    formatDate: formatDate,
    convertSizeToRatio: convertSizeToRatio
  };
</wxs>

<!-- Feed图片弹窗层 -->
<view wx:if="{{showFeedImageModal}}" class="feed-image-modal-mask" bindtap="closeFeedImageModal">
  <view class="feed-image-modal" catchtap="">
    <image class="feed-image-modal-img" src="{{feedImageModalData.displayImageURL ? feedImageModalData.displayImageURL : feedImageModalData.imageURL}}" mode="widthFix" />
    <view class="feed-image-modal-info">
      <view>模型：{{feedImageModalData.model}}</view>
      <view>尺寸：{{feedImageModalData.width}} x {{feedImageModalData.height}}</view>
      <!-- <view>种子：{{feedImageModalData.seed}}</view> -->
      <!-- <view wx:if="{{feedImageModalData.enhance}}">增强：是</view>
      <view wx:else>增强：否</view>
      <view>状态：{{feedImageModalData.status}}</view>
      <view>提示词：{{feedImageModalData.prompt}}</view> -->
    </view>
    <button class="feed-image-modal-same" bindtap="doSameFeedImage" disabled="{{!showDoSameBtn}}">{{!showDoSameBtn?'私有作品':'做同款'}}</button>
    <button class="feed-image-modal-close" bindtap="closeFeedImageModal">关闭</button>
  </view>
</view>



