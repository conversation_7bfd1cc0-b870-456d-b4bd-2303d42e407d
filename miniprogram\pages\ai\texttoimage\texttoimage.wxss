.texttoimage-container {
  margin: 0 20rpx;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.97);
  /* border-radius: 28rpx;
  box-shadow: 0 12rpx 30rpx rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(15px);
  animation: slideUp 0.6s cubic-bezier(0.16, 1, 0.3, 1); */
}
/* @keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.03); }
  100% { transform: scale(1); }
} */

.bg-image {
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: -1;
  /* filter: brightness(1.05) saturate(1.1); */
  transition: all 0.5s ease;
}

.input-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.97), rgba(255, 255, 255, 0.92));
  border-radius: 24rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.06), 
              inset 0 1px 1px rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  padding: 35rpx;
}

/* 提示词输入框样式 */
.prompt-input-container {
  position: relative;
  width: 100%;
  margin-bottom: 20rpx;
}

.prompt-input {
  width: 100%;
  padding: 28rpx;
  border-radius: 18rpx;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04),
              inset 0 2rpx 5rpx rgba(0, 0, 0, 0.03);
  border: 1px solid rgba(0, 0, 0, 0.08);
  font-size: 30rpx;
  color: #333;
  min-height: 180rpx;
  max-height: 320rpx;
  line-height: 1.6;
  box-sizing: border-box;
  transition: all 0.25s ease;
}

/* 提示词工具按钮容器 */
.prompt-tools {
  display: flex;
  width: 100%;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

/* 工具按钮基础样式 */
.tool-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 0;
  border-radius: 12rpx;
  font-size: 28rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  border: none;
  transition: all 0.2s ease;
}

.tool-btn:active {
  transform: scale(0.98);
}

.tool-btn-icon {
  margin-right: 10rpx;
  font-size: 30rpx;
}

.tool-btn-text {
  font-weight: 500;
}

/* 翻译按钮样式 */
.translate-btn {
  background: linear-gradient(135deg, #64B5F6, #2196F3);
  color: white;
}

/* 扩充按钮样式 */
.expand-btn {
  background: linear-gradient(135deg, #AED581, #8BC34A);
  color: white;
}

/* 随机提示词按钮样式 */
.random-btn {
  background: linear-gradient(135deg, #7C4DFF, #64B5F6);
  color: white;
}

/* 字数提示美化 */
.prompt-length {
  position: absolute;
  bottom: -8rpx;
  right: 22rpx;
  font-size: 24rpx;
  color: #999;
  background: rgba(255, 255, 255, 0.8);
  padding: 4rpx 10rpx;
  border-radius: 10rpx;
}

/* 尺寸选择器样式 */
.size-selector {
  width: 100%;
  margin-bottom: 30rpx;
  /* position: relative; */
}

.size-scroll {
  width: 100%;
  white-space: nowrap;
  padding: 10rpx 10rpx;
  /* display: flex; */
  min-height: 130rpx; /* 确保滚动视图有足够高度 */
  align-items: center;
  overflow-x: auto;
}


/* 选择器标题美化 */
.selector-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 22rpx;
  display: flex;
  align-items: center;
}

.selector-title::before {
  content: "";
  display: inline-block;
  width: 8rpx;
  height: 28rpx;
  background: linear-gradient(to bottom, #4CAF50, #2E7D32);
  border-radius: 4rpx;
  margin-right: 12rpx;
}


/* 尺寸选项优化 */
.size-option {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  justify-content: center; /* 居中显示内容 */
  padding: 20rpx 15rpx;
  margin-right: 10rpx;
  border-radius: 16rpx;
  /* background: rgba(255, 255, 255, 0.9); */
  border: 1px solid rgba(0, 0, 0, 0.06);
  /* transition: all 0.3s ease; */
  width: 120rpx; /* 统一宽度为150rpx */
  height: 140rpx; /* 确保高度足够 */
  box-sizing: border-box;
  /* box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.02); */
}

.size-option:first-child,.style-option:first-child {
  margin-top: 5rpx;
  margin-left: 5rpx;
}
.size-option:active {
  transform: scale(0.95);
}

.size-option.selected {
  background: rgba(76, 175, 80, 0.15);
  border-color: rgba(76, 175, 80, 0.5);
  box-shadow: 0 6rpx 16rpx rgba(76, 175, 80, 0.15);
  transform: translateY(-2rpx) scale(1.02);
}

/* 尺寸图标样式 */
.size-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  box-sizing: border-box;
}

/* 所有图标共享的样式 */
.square,
.vertical-rectangle,
.vertical-rectangle-mild,
.horizontal-rectangle,
.horizontal-rectangle-mild,
.horizontal-rectangle-wide,
.vertical-rectangle-tall {
  background-color: #f5f5f5;
  border: 2px solid #666;
  display: block;
}

/* 1:1 正方形 */
.square {
  width: 60rpx;
  height: 60rpx;
}

/* 9:16 垂直长方形 */
.vertical-rectangle {
  width: 36rpx;
  height: 64rpx;
}

/* 3:4 垂直温和长方形 */
.vertical-rectangle-mild {
  width: 48rpx;
  height: 64rpx;
}

/* 16:9 水平长方形 */
.horizontal-rectangle {
  width: 64rpx;
  height: 36rpx;
  margin-top: 20rpx;
}

/* 4:3 水平温和长方形 */
.horizontal-rectangle-mild {
  width: 64rpx;
  height: 48rpx;
  margin-top: 20rpx;
}

/* 2:1 水平宽长方形 */
.horizontal-rectangle-wide {
  width: 64rpx;
  height: 32rpx;
  margin-top: 20rpx;
}

/* 1:2 垂直高长方形 */
.vertical-rectangle-tall {
  width: 32rpx;
  height: 64rpx;
}

/* 选中状态样式 */
.size-option.selected .square,
.size-option.selected .vertical-rectangle,
.size-option.selected .vertical-rectangle-mild,
.size-option.selected .horizontal-rectangle,
.size-option.selected .horizontal-rectangle-mild,
.size-option.selected .horizontal-rectangle-wide,
.size-option.selected .vertical-rectangle-tall {
  border-color: #4CAF50;
  background-color: rgba(76, 175, 80, 0.15);
}

/* 标签样式 */
.size-label {
  font-size: 24rpx;
  color: #555;
  line-height: 1.2;
  text-align: center;
  width: 100%;
}

.size-option.selected .size-label {
  color: #4CAF50;
  font-weight: 500;
}

/* 按钮容器样式 */
.button-container {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 30rpx;
  padding-bottom: 10rpx;
}

.generate-btn {
  width: 100%;
  padding: 28rpx 0;
  background: linear-gradient(135deg, #1E88E5, #0160b3);
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  letter-spacing: 2rpx;
  border-radius: 18rpx;
  border: none;
  box-shadow: 0 8rpx 20rpx rgba(76, 175, 80, 0.3);
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  position: relative;
  overflow: hidden;
}

.generate-btn::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transform: rotate(45deg);
  animation: shine 3s infinite cubic-bezier(0.16, 1, 0.3, 1);
}

.generate-btn:active {
  transform: scale(0.97);
  box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.25);
}

.generate-btn[disabled] {
  background: linear-gradient(135deg, #cccccc, #bbbbbb);
  box-shadow: none;
  color: rgba(255, 255, 255, 0.8);
}

/* 进度条样式 */
.progress-container {
  margin: 0 20rpx;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.97);
  border-radius: 28rpx;
  box-shadow: 0 12rpx 30rpx rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(15px);
  animation: slideUp 0.6s cubic-bezier(0.16, 1, 0.3, 1);
  margin-bottom: 20rpx;
}

.progress-text {
  font-size: 26rpx;
  color: #555;
  text-align: center;
  margin-top: 16rpx;
  font-weight: 500;
}

/* 结果展示区域样式 */
.result-section {
  margin: 0 20rpx;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.97);
  border-radius: 28rpx;
  box-shadow: 0 12rpx 30rpx rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(15px);
  animation: slideUp 0.6s cubic-bezier(0.16, 1, 0.3, 1);
  margin-bottom: 20rpx;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.result-title {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 30rpx;
  color: #333;
  text-align: center;
  position: relative;
  padding-bottom: 20rpx;
}

.result-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #43A047, #2E7D32);
  border-radius: 2rpx;
}

.generated-image {
  width: 100%;
  border-radius: 18rpx;
  box-shadow: 0 10rpx 25rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 35rpx;
  transition: all 0.3s ease;
}

.generated-image:active {
  transform: scale(0.98);
}

.image-actions {
  display: flex;
  gap: 25rpx;
  justify-content: center;
  margin-top: 10rpx;
}

.action-btn {
  flex: 1;
  padding: 22rpx 0;
  border-radius: 14rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
}

.save-btn {
  background: linear-gradient(135deg, #43A047, #2E7D32);
  color: white;
  box-shadow: 0 6rpx 16rpx rgba(76, 175, 80, 0.25);
  letter-spacing: 1rpx;
}

.preview-btn {
  background: linear-gradient(135deg, #1E88E5, #1565C0);
  color: white;
  box-shadow: 0 6rpx 16rpx rgba(33, 150, 243, 0.25);
  letter-spacing: 1rpx;
}

.action-btn:active {
  transform: scale(0.97);
  box-shadow: 0 3rpx 10rpx rgba(0, 0, 0, 0.2);
}

/* 工具介绍区域样式 */
.tool-intro {
  margin: 50rpx 20rpx 40rpx;
  padding: 35rpx;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.97), rgba(255, 255, 255, 0.92));
  border-radius: 28rpx;
  box-shadow: 0 12rpx 30rpx rgba(0, 0, 0, 0.07);
}

.intro-header {
  display: flex;
  align-items: center;
  margin-bottom: 35rpx;
}

.intro-icon {
  width: 90rpx;
  height: 90rpx;
  background: linear-gradient(135deg, #43A047, #2E7D32);
  border-radius: 22rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  box-shadow: 0 6rpx 16rpx rgba(76, 175, 80, 0.2);
}

.intro-icon image {
  width: 56rpx;
  height: 56rpx;
}

.intro-title {
  font-size: 38rpx;
  font-weight: 600;
  color: #333;
  letter-spacing: 1rpx;
}

.intro-content {
  padding: 10rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  display: block;
}

/* 功能列表样式 */
.feature-list {
  margin-bottom: 45rpx;
}

.feature-item {
  display: flex;
  margin-bottom: 30rpx;
}

.feature-item:last-child {
  margin-bottom: 0;
}

.feature-icon {
  width: 44rpx;
  height: 44rpx;
  border-radius: 12rpx;
  background: linear-gradient(135deg, #43A047, #2E7D32);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 22rpx;
  flex-shrink: 0;
  box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.2);
}

.feature-icon .text {
  color: white;
  font-size: 24rpx;
}

.feature-content {
  flex: 1;
}

.feature-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.feature-desc {
  font-size: 26rpx;
  color: #666;
  display: block;
  line-height: 1.5;
}

/* 步骤列表样式 */
.step-list {
  margin-bottom: 45rpx;
}

.step-item {
  display: flex;
  margin-bottom: 30rpx;
  position: relative;
}

.step-item:last-child {
  margin-bottom: 0;
}

.step-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 20rpx;
  top: 45rpx;
  bottom: -15rpx;
  width: 2rpx;
  background: rgba(76, 175, 80, 0.3);
}

.step-number {
  width: 44rpx;
  height: 44rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #43A047, #2E7D32);
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 22rpx;
  font-size: 24rpx;
  font-weight: 600;
  flex-shrink: 0;
  box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.2);
  z-index: 1;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}

.step-desc {
  font-size: 26rpx;
  color: #666;
  display: block;
  line-height: 1.5;
}

/* 提示列表样式 */
.tip-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.tip-item {
  background: rgba(255, 255, 255, 0.85);
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.04);
  position: relative;
  overflow: hidden;
}

.tip-item::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.8), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.tip-item:active {
  transform: scale(0.98);
}

.tip-item:active::after {
  transform: translateX(100%);
}

.tip-icon {
  font-size: 30rpx;
  margin-bottom: 12rpx;
}

.tip-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.tip-text {
  font-size: 24rpx;
  color: #666;
  display: block;
  line-height: 1.5;
}

/* 进度条美化 */
progress {
  border-radius: 999rpx;
  overflow: hidden;
  height: 30rpx;
}

/* 自定义进度条颜色 */
progress .wx-progress-inner-bar {
  border-radius: 999rpx;
  transition: all 0.3s ease;
}

/* 内容滚动区域 */
.content-scroll {
  padding: 20rpx;
  box-sizing: border-box;
  height: 100%;
  transition: all 0.3s ease;
}

.content-scroll.no-scroll {
  overflow: hidden;
}


.section-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 20rpx;
  color: #333;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: "";
  display: inline-block;
  width: 8rpx;
  height: 28rpx;
  background: linear-gradient(to bottom, #4CAF50, #2E7D32);
  border-radius: 4rpx;
  margin-right: 12rpx;
}

/* 警告提示优化 */
.storage-notice {
  font-size: 24rpx;
  color: #ff4d4f;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  background: rgba(255, 77, 79, 0.08);
  padding: 12rpx 16rpx;
  border-radius: 12rpx;
  border-left: 4rpx solid #ff4d4f;
}

.storage-notice::before {
  content: "⚠️";
  margin-right: 10rpx;
  font-size: 28rpx;
}

/* 新的警告提示样式 */
.warning-notice {
  background-color: #fff1f0;
  border: 1px solid #ffccc7;
  border-radius: 8rpx;
  padding: 16rpx 20rpx;
  margin-bottom: 20rpx;
}

.warning-item {
  font-size: 24rpx;
  color: #ff4d4f;
  display: block;
  padding: 0 10rpx;
}


.prompt-text {
  font-size: 26rpx;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  margin-bottom: 10rpx;
  /* 添加可点击的视觉提示 */
  position: relative;
  /* padding-left: 10rpx; */
  transition: all 0.2s ease;
}

/* 添加可点击效果 */
.prompt-text:active {
  background-color: rgba(76, 175, 80, 0.1);
  border-radius: 4rpx;
}

/* 添加提示图标 */
.prompt-text::before {
  content: "🔍";
  font-size: 22rpx;
  margin-right: 6rpx;
  opacity: 0.6;
}

.image-size {
  font-size: 22rpx;
  color: #888;
  display: flex;
  align-items: center;
}

.image-size::before {
  content: "📏";
  margin-right: 6rpx;
}

/* 图像元数据区域 */
.image-meta {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  margin-top: 8rpx;
}

/* 时间显示样式 */
.image-time {
  font-size: 22rpx;
  color: #888;
  display: flex;
  align-items: center;
}

.image-time::before {
  content: "🕒";
  margin-right: 6rpx;
}

/* 删除按钮优化 */
.delete-button {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  width: 44rpx;
  height: 44rpx;
  border-radius: 22rpx;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.delete-button:active {
  transform: scale(0.9);
  background-color: rgba(255, 77, 79, 0.8);
}

.delete-icon {
  color: #fff;
  font-size: 30rpx;
  line-height: 1;
  font-weight: bold;
}

/* 分页控件样式 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 24rpx 0;
  margin-top: 24rpx;
}

.pagination-btn {
  padding: 12rpx 32rpx;
  background-color: #1E88E5;
  color: white;
  border-radius: 10rpx;
  font-size: 26rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.25);
  transition: all 0.3s ease;
}

.pagination-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.2);
}

.pagination-btn.disabled {
  background-color: #dddddd;
  color: #999999;
  box-shadow: none;
}

.pagination-info {
  margin: 0 24rpx;
  font-size: 26rpx;
  color: #555;
  background: rgba(255, 255, 255, 0.8);
  padding: 8rpx 16rpx;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* 风格选择器样式 */
.style-selector {
  width: 100%;
  margin-bottom: 30rpx;
  /* position: relative; */
}

.style-scroll {
  width: 100%;
  white-space: nowrap;
  padding: 10rpx 10rpx;
  /* display: flex; */
  align-items: center;
  min-height: 220rpx; /* 确保滚动视图有足够高度 */
  overflow-x: auto;
}

.style-option {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10rpx;
  margin:10rpx 20rpx 10rpx 0;
  border-radius: 16rpx;
  border: 1px solid rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  width: 180rpx; /* 统一宽度 */
  height: 220rpx; /* 增加高度 */
  box-sizing: border-box;
  overflow: hidden;
}

.style-img-container {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  overflow: hidden;
}

.style-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.style-label-container {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background: rgba(0, 0, 0, 0.6);
  text-align: center;
}

.style-label {
  font-size: 24rpx;
  font-weight: 500;
  color: #ffffff;
  text-align: center;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.style-option.selected {
  background: transparent;
  border-color: rgba(76, 175, 80, 0.8);
  box-shadow: 0 6rpx 16rpx rgba(76, 175, 80, 0.25);
  transform: translateY(-2rpx) scale(1.05);
}

.style-option:active {
  transform: scale(0.98);
}

.style-option.selected .style-img-container {
  box-shadow: 0 0 0 3rpx rgba(76, 175, 80, 0.8) inset;
}

.style-option.selected .style-label-container {
  background: rgba(76, 175, 80, 0.8);
}

/* 滚动增强效果 */
.size-scroll::-webkit-scrollbar,
.style-scroll::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}

.scroll-spacer {
  display: inline-block;
  width: 30rpx;
  height: 1rpx;
}

@keyframes scrollHint {
  0% { transform: translateX(0); }
  25% { transform: translateX(10rpx); }
  50% { transform: translateX(0); }
  75% { transform: translateX(10rpx); }
  100% { transform: translateX(0); }
}


/* 添加使用次数提示和分享按钮样式 */
.usage-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 20rpx;
  width: 100%;
}

.usage-count {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.share-btn {
  background: linear-gradient(145deg, #e53935, #b71c1c); /* 红色渐变 */
  color: white;
  font-size: 28rpx;
  padding: 16rpx 30rpx;
  border-radius: 40rpx;
  border: none;
  box-shadow: 0 6rpx 12rpx rgba(229, 57, 53, 0.25);
  margin-top: 10rpx;
  font-weight: 500;
  transition: all 0.3s;
}

.share-btn:active {
  transform: scale(0.96);
  box-shadow: 0 3rpx 6rpx rgba(229, 57, 53, 0.15);
}

/* 分享状态提示样式 */
.share-status {
  font-size: 26rpx;
  color: #3178ca;
  margin-top: 16rpx;
  padding: 10rpx 20rpx;
  background-color: rgba(49, 120, 202, 0.1);
  border-radius: 30rpx;
  font-weight: 500;
  animation: pulse 1.5s infinite ease-in-out;
}

@keyframes pulse {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

/* 添加API选择器样式 */
.api-selector {
  width: 100%;
  margin-bottom: 30rpx;
}

.api-options {
  display: flex;
  gap: 20rpx;
  margin-bottom: 10rpx;
}

.api-option {
  flex: 1;
  padding: 20rpx 15rpx;
  text-align: center;
  border-radius: 16rpx;
  border: 1px solid rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  position: relative; /* 添加相对定位，以便角标可以相对于此定位 */
}

.api-option:active {
  transform: scale(0.95);
}

.api-option.selected {
  background: rgba(76, 175, 80, 0.15);
  border-color: rgba(76, 175, 80, 0.5);
  box-shadow: 0 6rpx 16rpx rgba(76, 175, 80, 0.15);
}

.api-label {
  font-size: 28rpx;
  color: #555;
  font-weight: 500;
}

.api-option.selected .api-label {
  color: #4CAF50;
}

/* 速度角标样式 */
.speed-badge {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  font-size: 20rpx;
  /* font-weight: bold; */
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  color: white;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
}

.speed-fast {
  background: linear-gradient(135deg, #4CAF50, #2E7D32);
  animation: pulse-badge 2s infinite ease-in-out;
}

.speed-slow {
  background: linear-gradient(135deg, #FF9800, #F57C00);
}

@keyframes pulse-badge {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.selector-box {
  background: #fff;
  border-radius: 12rpx;
  padding: 10rpx 18rpx;
  border: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.keep-subject-switch {
  margin-bottom: 24rpx;
}

.picker-inner {
  font-size: 28rpx;
  color: #333;
}

.model-and-subject-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  border-radius: 18rpx;
  gap: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03);
}

.pollinations-model-picker {
  flex: 1;
  min-width: 0;
}

.model-name {
  margin-left: 8rpx;
  font-weight: 500;
  color: #2196F3;
}

.subject-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 生成设置折叠区域样式 */
.settings-toggle-container {
  width: 100%;
  margin-bottom: 30rpx;
}
.settings-toggle-btn {
  width: 100%;
  padding: 22rpx 0;
  background: linear-gradient(135deg, #f5f5f5, #e0e0e0);
  color: #333;
  font-size: 30rpx;
  font-weight: 600;
  border-radius: 14rpx;
  border: 1px solid #d0d0d0;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 18rpx;
  transition: all 0.2s;
}
.settings-toggle-btn:active {
  background: #e0e0e0;
}
.settings-arrow {
  margin-left: 18rpx;
  font-size: 28rpx;
}
.settings-panel {
  background: #fafafa;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.04);
  padding: 24rpx 10rpx 10rpx 10rpx;
  margin-bottom: 10rpx;
  animation: fadeIn 0.3s;
}

.settings-btn {
  background: linear-gradient(135deg, #f5f5f5, #e0e0e0);
  color: #333;
  border: 1px solid #d0d0d0;
  font-weight: 600;
}

.settings-btn:active {
  background: #e0e0e0;
}

/* 交互禁用层样式 */
.interaction-disabled-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10; /* 确保在内容之上 */
  pointer-events: auto; /* 阻止下方元素的点击事件 */
  /* 可以选择添加一个半透明背景 */
  /* background-color: rgba(255, 255, 255, 0.1); */
}

/* Clear button style */
.clear-prompt-btn {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  z-index: 10;
  padding: 4rpx 12rpx;
  /* width: 40rpx;
  height: 40rpx; */
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border-radius: 20rpx;
  font-size: 20rpx;
}

/* Feed区美化 */
.feed-section {
  margin: 30rpx 20rpx 40rpx;
  padding: 15rpx 20rpx 30rpx 20rpx;
  background:#fff;
  border-radius: 28rpx;
  box-shadow: 0 8rpx 24rpx rgba(76, 175, 80, 0.08);
  animation: fadeIn 0.5s;
}

.feed-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
  justify-content: flex-start;
  margin-bottom: 20rpx;
}

.feed-item {
  width: calc(50% - 14rpx);
  min-width: 0;
  max-width: none;
  background: #fff;
  border-radius: 18rpx;
  box-shadow: 0 4rpx 16rpx rgba(76, 175, 80, 0.10);
  margin-bottom: 10rpx;
  overflow: hidden;
  position: relative;
  transition: box-shadow 0.2s;
  animation: fadeIn 0.5s;
}
.feed-item:active {
  box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.18);
}

.feed-image-wrapper {
  width: 100%;
  aspect-ratio: 1/1;
  background: #f5f5f5;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
.feed-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 12rpx 12rpx 0 0;
  box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.08);
  transition: transform 0.2s;
  background: #f5f5f5;
}
.feed-image:active {
  transform: scale(0.97);
}
.feed-image-loading {
  position: absolute;
  left: 0; right: 0; top: 0; bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255,255,255,0.7);
  color: #43A047;
  font-size: 28rpx;
  font-weight: 500;
  z-index: 2;
  border-radius: 12rpx 12rpx 0 0;
  animation: fadeIn 0.3s;
}
.feed-meta {
  padding: 18rpx 16rpx 14rpx 16rpx;
  background: #fafafa;
  border-radius: 0 0 12rpx 12rpx;
  border-top: 1px solid #f0f0f0;
  min-height: 48rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.feed-info {
  font-size: 24rpx;
  color: #666;
  margin-top: 2rpx;
  letter-spacing: 1rpx;
  font-weight: 400;
}

/* 分页控件美化 */
.feed-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 18rpx 0 0 0;
  margin-top: 10rpx;
}


.feed-loading {
  text-align: center;
  color: #43A047;
  font-size: 28rpx;
  margin: 30rpx 0 20rpx 0;
  font-weight: 500;
}

.feed-empty {
  text-align: center;
  color: #bbb;
  font-size: 26rpx;
  margin: 30rpx 0 20rpx 0;
}

/* Feed图片弹窗层样式 */
.feed-image-modal-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.55);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.feed-image-modal {
  background: #fff;
  border-radius: 22rpx;
  box-shadow: 0 12rpx 32rpx rgba(0,0,0,0.18);
  padding: 32rpx 24rpx 24rpx 24rpx;
  max-width: 90vw;
  min-width: 60vw;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: fadeIn 0.25s;
}
.feed-image-modal-img {
  width: 100%;
  max-width: 520rpx;
  max-height: 48vh;
  object-fit: contain;
  border-radius: 14rpx;
  box-shadow: 0 4rpx 16rpx rgba(76, 175, 80, 0.10);
  margin-bottom: 28rpx;
  background: #f5f5f5;
}
.feed-image-modal-info {
  width: 100%;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 24rpx;
  line-height: 1.7;
  text-align: left;
}
.feed-image-modal-close {
  width: 100%;
  padding: 18rpx 0;
  background: #e6e5e5;
  color: #666;
  border-radius: 12rpx;
  font-size: 30rpx;
  font-weight: 600;
  border: none;
  box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.13);
  margin-top: 10rpx;
}

.feed-image-modal-same {
  width: 100%;
  padding: 18rpx 0;
  background:#1E88E5;
  color: #fff;
  border-radius: 12rpx;
  font-size: 30rpx;
  font-weight: 600;
  border: none;
  box-shadow: 0 2rpx 8rpx rgba(33, 150, 243, 0.13);
  margin-bottom: 16rpx;
}

.increase-tag-container {
  display: inline-block;
  margin-left: 16rpx;
  vertical-align: middle;
}
.increase-tag {
  display: inline-block;
  background: linear-gradient(135deg, #ff9800, #f44336);
  color: #fff;
  font-size: 22rpx;
  padding: 4rpx 16rpx;
  border-radius: 16rpx;
  font-weight: 500;
  margin-left: 4rpx;
  cursor: pointer;
  transition: background 0.2s;
}
.increase-tag:active {
  background: linear-gradient(135deg, #f44336, #ff9800);
}
.increase-tip-detail {
  margin-top: 12rpx;
  background: #fffbe6;
  color: #d48806;
  font-size: 24rpx;
  border-radius: 10rpx;
  padding: 14rpx 18rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 193, 7, 0.08);
  line-height: 1.7;
}

.custom-loading {
  width: 60rpx;
  height: 60rpx;
  margin: 0 auto 18rpx auto;
  border: 6rpx solid #e0e0e0;
  border-top: 6rpx solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.section-title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.my-assets-link {
  color: #1E88E5;
  font-size: 28rpx;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
  margin-left: 24rpx;
  margin-bottom: 10rpx;
  min-width: 0;
  height: auto;
  text-align: right;
  display: flex;
  align-items: center;
  border: 1px solid #ccc;
  padding: 10rpx 20rpx;
  border-radius: 10rpx;
}
.my-assets-link:active,
.my-assets-link:hover {
  color: #1565C0;
  text-decoration: none;
}

/* Feed刷新按钮样式 */
.feed-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.refresh-feed-btn {
  color: #666;
  font-size: 26rpx;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  border: 1px solid #ddd;
  border-radius: 8rpx;
  background: #f8f8f8;
  transition: all 0.3s ease;
}

.refresh-feed-btn:active {
  background: #e8e8e8;
  color: #333;
}

.refresh-icon {
  font-size: 30rpx;
  font-weight: bold;
}

