// index.ts
import { layoutUtil } from '../../../utils/layout';
import eventBus from '../../../utils/eventBus';
import { STATIC_URL } from '../../../utils/constants';
import {
  showErrorMessage,
  updateProgress as updateProgressUtil,
  handleTabBarChange as handleTabBarChangeUtil,
  saveImageToAlbum,
  startButtonCooldown
} from '../../../utils/aiUtils';
import ai from '../../../utils/ai';
const { pollinations, zhipu } = ai;
import { getSettingWithDefault, getGroupSettingsWithDefault } from '../../../utils/appSettings';

// Feed数据类型定义
interface FeedItem {
  imageURL: string;
  displayImageURL?: string;
  model?: string;
  width?: number;
  height?: number;
  seed?: string;
  enhance?: boolean;
  status?: string;
  prompt?: string;
}

// ====== 阶段进度区间与提示语配置 ======
const QUEUED_PROGRESS_START = 10;
const QUEUED_PROGRESS_END = 40;
const PROCESSING_PROGRESS_START = 41;
const PROCESSING_PROGRESS_END = 99;
const QUEUED_HINTS = [
  '排队中...',
  '正在分配算力...',
  '等待资源...',
  'AI正在排队，请耐心等待...'
];
const PROCESSING_HINTS = [
  '正在绘制...',
  '正在渲染图片...',
  '画面渲染中...',
  '图像生成中...',
  '绘制进行中...',
  '构思画面...',
  '渲染即将完成...',
  '创意生成中...',
  '细节优化中...',
  '笔触运作...',
  '画面构建中...',
  '色彩调整中...'
];

// 高质量flux模型关键词组（人物为主，专业精致）
const fluxPrompts: string[] = [
  ' 唯美精致二次元少女，银白瀑布长发，紫罗兰色虹膜瞳孔，温婉微笑，45 度优雅侧脸，柔光漫射照明，8K 超高清分辨率，细腻光滑皮肤质感，顶级 CG 插画渲染 ',
  ' 赛博朋克未来感少年，利落短发造型，机械义肢细节丰富，冷色调蓝紫色系，霓虹璀璨赛博朋克城市夜景，镜面反光雨地效果，动态张力构图，电影级视觉表现 ',
  ' 东方古典美学古风美人，长发高盘发髻，流苏金饰点缀，水墨晕染背景，淡雅低饱和度色彩，写意中国风水墨笔触，传统工笔画质感 ',
  ' 国际时尚超模，金色波浪卷发，碧绿眼眸，烈焰红唇，黑色高级定制礼服，专业棚拍环境，硬光直射打光，极简纯色背景，4K 高清商业摄影风格 ',
  ' 写实主义男性肖像，浓密络腮胡，深邃凝视眼神，低饱和大地色系，厚重油画笔触肌理，强烈明暗对比，伦勃朗式光线效果 ',
  'Q 版可爱萝莉形象，双马尾发型，粉色蓬蓬连衣裙，浪漫花海场景，逆光光晕渲染，梦幻柔焦氛围，高精度动漫插画风格 ',
  ' 成熟中年绅士，修身西装套装，金属框眼镜，复古书房背景，暖黄色调布光，超写实绘画风格，丰富细节纹理刻画 ',
  ' 活力运动少女，清爽短发，专业运动服饰，奔跑动态姿态，动感模糊特效，阳光明媚草地场景，高清体育摄影质感 ',
  ' 阳光黑皮肤男孩，卷曲发型，灿烂开怀笑容，街头涂鸦艺术背景，鲜艳高对比色彩，三维 CG 建模渲染风格 ',
  ' 暗黑哥特风少女，黑色曳地长裙，精致蕾丝装饰，暗色玫瑰元素，清冷月光照明，神秘暗黑氛围，精细手绘插画质感 ',
  ' 唯美精致二次元少女，银白瀑布长发，紫罗兰色虹膜瞳孔，温婉微笑，45 度优雅侧脸，柔光漫射照明，8K 超高清分辨率，细腻光滑皮肤质感，顶级 CG 插画渲染 ',
  ' 赛博朋克未来感少年，利落短发造型，机械义肢细节丰富，冷色调蓝紫色系，霓虹璀璨赛博朋克城市夜景，镜面反光雨地效果，动态张力构图，电影级视觉表现 ',
  ' 东方古典美学古风美人，长发高盘发髻，流苏金饰点缀，水墨晕染背景，淡雅低饱和度色彩，写意中国风水墨笔触，传统工笔画质感 ',
  ' 国际时尚超模，金色波浪卷发，碧绿眼眸，烈焰红唇，黑色高级定制礼服，专业棚拍环境，硬光直射打光，极简纯色背景，4K 高清商业摄影风格 ',
  ' 写实主义男性肖像，浓密络腮胡，深邃凝视眼神，低饱和大地色系，厚重油画笔触肌理，强烈明暗对比，伦勃朗式光线效果 ',
  'Q 版可爱萝莉形象，双马尾发型，粉色蓬蓬连衣裙，浪漫花海场景，逆光光晕渲染，梦幻柔焦氛围，高精度动漫插画风格 ',
  ' 成熟中年绅士，修身西装套装，金属框眼镜，复古书房背景，暖黄色调布光，超写实绘画风格，丰富细节纹理刻画 ',
  ' 活力运动少女，清爽短发，专业运动服饰，奔跑动态姿态，动感模糊特效，阳光明媚草地场景，高清体育摄影质感 ',
  ' 阳光黑皮肤男孩，卷曲发型，灿烂开怀笑容，街头涂鸦艺术背景，鲜艳高对比色彩，三维 CG 建模渲染风格 ',
  ' 暗黑哥特风少女，黑色曳地长裙，精致蕾丝装饰，暗色玫瑰元素，清冷月光照明，神秘暗黑氛围，精细手绘插画质感 '
];

// @ts-ignore
/// <reference types="miniprogram-api-typings" />
declare const wx: any;

Component({
  data: {
    ...layoutUtil.getLayoutInfo(),
    ...layoutUtil.All_Size(),
    isTabBarCollapsed: true,
    layoutStyle: layoutUtil.getContentStyle_nosafeArea(),
    loading: false,
    imageSizes: [],
    imageStyles: [],
    pageSize: 4,
    shareNumber: 10,
    totalcontroltime: [],
    shareCountdownTime: 10,
    prompt: '',
    selectedSizeIndex: 0,
    selectedStyleIndex: 0,
    pollinationsModels: [],
    Pollinations_modelList: false,
    selectedPollinationsModelIndex: 0,
    generatedImageResult: null,
    generating: false,
    keepSubject: false,
    lastSeed: null,
    // 新增：生成设置折叠控制
    showSettings: false,
    // 新增：控制页面交互禁用状态
    isInteractingDisabled: false,
    feedList: [], // 远程feed数据
    feedLoading: false, // feed加载状态
    feedPage: 1, // 当前页码
    feedPageSize: 10, // 每页数量
    feedTotal: 0, // feed总数
    // 删除所有与本地缓存相关的读取和写入逻辑，保证fetchOfficialFeed每次都即时请求
    currentFeedPageList: [], // 当前页feed数据
    feedImageLoaded: [], // 当前页图片加载状态
    showFeedImageModal: false, // feed图片弹窗显示
    feedImageModalData: null, // feed图片弹窗数据
    scrollTop: 0,
    // 分页相关
    totalPages: 1,
    isPrevDisabled: true,
    isNextDisabled: false,
    showDoSameBtn: true,
    // 简化：统一按钮冷却倒计时对象
    buttonCooldowns: {
      expand: { cooldown: 0, disabled: false, timer: null },
      translate: { cooldown: 0, disabled: false, timer: null }
    },
    queuePosition: null, // 当前排队位置
    totalQueued: null, // 当前排队总数
    progress: 0, // 进度条唯一数据源
    // ====== 新增：生成次数与按钮显示控制 ======
    generateCount: 0, // 当前已生成次数
    showGenerateBtn: true, // 是否显示生成图片按钮
    showShareBtn: false, // 是否显示分享按钮
    generateCountStorageKey: 'texttoimage_generate_count', // 本地存储key
    // ====== 新增：分享冷却相关 ======
    lastShareTime: 0, // 上次分享时间戳
    lastShareTimeStorageKey: 'texttoimage_last_share_time',
    shareCooldownTimer: null, // 冷却定时器ID
    shareBtnText: '分享给好友继续使用', // 分享按钮文字
    // ====== 新增：分享递增上限相关 ======
    currentShareNumber: 0, // 当前动态生成上限
    shareTimes: 0, // 已正常分享次数
    shareDate: '', // 上次递增数据日期
    currentShareNumberStorageKey: 'texttoimage_current_share_number',
    shareTimesStorageKey: 'texttoimage_share_times',
    shareDateStorageKey: 'texttoimage_share_date',
    showIncreaseTip: false, // 是否展开增加上限说明
    pollingInterval: 10, // 轮询间隔（秒），改为10秒
    webSocketTaskId: null, // 当前WebSocket任务ID
    webSocketConnected: false, // WebSocket连接状态
    progressText: '', // 进度提示文本
    // 新增：登录状态和用户信息
    isLogin: false,
    userInfo: null,
    ImageError: false,
    ImageErrortitle: '不好意思，跑的太快了，没看清楚关键词，请重新尝试一下吧。',
    // 新增：用于存储 SocketTask 实例，替换自定义 WebSocketService
    wsService: null, // 用于存储 wx.connectSocket 返回的 SocketTask 实例
    generationCooldownInterval: 10, // 生成按钮冷却时间，默认10秒
    feedPollingTimer: null, // 新增：Feed自动刷新定时器
    feedRetryTimer: null, // 新增：Feed重试定时器
    feedAutoUpdateTimer: null, // 新增：Feed自动更新定时器（每10秒）
    feedAutoUpdateInterval: 10000, // 自动更新间隔（毫秒）
    isAutoUpdating: false, // 是否正在自动更新中
    cachedNewItem: null, // 缓存的新数据项
    lastUpdateTime: 0, // 上次更新时间
    maxFeedItems: 50, // 最大feed数量（5页 × 10条/页）
  },
  lifetimes: {
    attached: async function () {
      // 优化事件监听
      this._bindEvents();
      try {
        const settings = await getGroupSettingsWithDefault('texttoimage', {}) as any;
        this.setData({
          imageSizes: settings.image_sizes || [],
          imageStyles: settings.image_styles || [],
          pageSize: settings.pagesize || 4,
          shareNumber: settings.sharenumber || 10,
          totalcontroltime: settings.totalcontroltime || [],
          shareCountdownTime: settings.sharecountdowntime || 10,
        });

        // Dynamically set generationCooldownInterval from settings, default to 10 seconds
        let generationAfterInterval = 10; // Default to 10 seconds
        if (settings && settings.totalcontroltime && settings.totalcontroltime.generation_after) {
          const interval = settings.totalcontroltime.generation_after.interval;
          if (typeof interval === 'number' && interval > 0) {
            generationAfterInterval = interval;
          }
        }
        this.setData({ generationCooldownInterval: generationAfterInterval });
      } catch (e) {
        console.warn('加载texttoimage配置失败，使用默认配置', e);
      }
      // console.log('totalcontroltime', this.data.totalcontroltime);
      // console.log('totalcontroltime.translation_button.interval', this.data.totalcontroltime.translation_button.interval);
      // eventBus.on('tabBarCollapseChange', this.handleTabBarChange.bind(this));
      // Pollinations模型列表加载
      try {
        const models = await pollinations.getModels() || [];
        // 检查是否包含gptimage
        // if (models.includes('gptimage')) {
        //   wx.showModal({
        //     title: '提示',
        //     content: 'gptimage模型暂停使用',
        //     showCancel: false
        //   });
        // }
        // 过滤掉gptimage
        const filteredModels = models.filter(m => m !== 'gptimage');
        let turboIndex = filteredModels.indexOf('turbo');
        if (turboIndex === -1) turboIndex = 0;
        if (filteredModels.length > 0) {
          this.setData({
            pollinationsModels: filteredModels,
            Pollinations_modelList: true,
            selectedPollinationsModelIndex: turboIndex
          });
        } else {
          this.setData({
            pollinationsModels: [],
            Pollinations_modelList: false,
            selectedPollinationsModelIndex: -1
          });
        }
      } catch (e) {
        console.error('获取Pollinations模型列表失败', e);
        this.setData({ Pollinations_modelList: false });
      }
      // this.loadFeedList(); // 移除旧的缓存接口调用，避免缓存命中
      // ====== 新增：初始化生成次数与按钮状态 ======
      let generateCount = 0;
      try {
        generateCount = wx.getStorageSync(this.data.generateCountStorageKey) || 0;
        if (typeof generateCount !== 'number') generateCount = 0;
      } catch (e) {
        generateCount = 0;
      }
      this.setData({ generateCount });
      // console.log('已用生成次数generateCount',generateCount);
      // console.log('生成上限shareNumber',this.data.shareNumber);
      // console.log('this.data.lastShareTime',this.data.lastShareTime);
      this.updateButtonStatus(generateCount, this.data.shareNumber, this.data.lastShareTime);
      // ====== 新增：初始化分享冷却时间 ======
      let lastShareTime = 0;
      try {
        lastShareTime = wx.getStorageSync(this.data.lastShareTimeStorageKey) || 0;
        if (typeof lastShareTime !== 'number') lastShareTime = 0;
      } catch (e) {
        lastShareTime = 0;
      }
      this.setData({ lastShareTime });
      // ====== 新增：初始化递增上限、分享次数、日期 ======
      let currentShareNumber = 0;
      let shareTimes = 0;
      let shareDate = '';
      const today = this._getTodayStr();
      try {
        currentShareNumber = wx.getStorageSync(this.data.currentShareNumberStorageKey) || 0;
        shareTimes = wx.getStorageSync(this.data.shareTimesStorageKey) || 0;
        shareDate = wx.getStorageSync(this.data.shareDateStorageKey) || '';
      } catch (e) {
        currentShareNumber = 0;
        shareTimes = 0;
        shareDate = '';
      }
      // 如果无数据或跨天，重置
      if (!currentShareNumber || !shareDate || shareDate !== today) {
        currentShareNumber = this.data.shareNumber;
        shareTimes = 0;
        shareDate = today;
        wx.setStorageSync(this.data.currentShareNumberStorageKey, currentShareNumber);
        wx.setStorageSync(this.data.shareTimesStorageKey, shareTimes);
        wx.setStorageSync(this.data.shareDateStorageKey, shareDate);
      }
      this.setData({ currentShareNumber, shareTimes, shareDate });
      this.checkAndResetShareData();
      // 动态读取轮询间隔
      let pollingInterval = 30;
      try {
        const appGroupSetting = wx.getStorageSync('appGroupSetting_texttoimage');
        if (appGroupSetting && appGroupSetting.value && appGroupSetting.value.totalcontroltime && appGroupSetting.value.totalcontroltime.polling && appGroupSetting.value.totalcontroltime.polling.interval) {
          pollingInterval = Number(appGroupSetting.value.totalcontroltime.polling.interval) || 30;
        }
      } catch (e) { pollingInterval = 30; }
      this.setData({ pollingInterval });
      // 添加登录状态更新监听
      // eventBus.on('loginStatusUpdate', this.handleLoginStatusUpdate.bind(this));
      eventBus.emit('registerLoginPage', { source: 'texttoimage' });

      // 尝试从本地缓存读取初始登录状态
      try {
        const token = wx.getStorageSync('token');
        const userInfo = wx.getStorageSync('userInfo');
        // console.log('token',token);
        // console.log('userInfo',userInfo);
        
        if (token && userInfo) {
          // console.log('[texttoimage] Initial login state from cache: true');
          this.setData({
            isLogin: true,
            userInfo: userInfo
          });
        } else {
          //  console.log('[texttoimage] Initial login state from cache: false');
           this.setData({
            isLogin: false,
            userInfo: null
          });
        }
      } catch (e) {
        // console.error('[texttoimage] Failed to read login state from cache:', e);
        this.setData({
          isLogin: false,
          userInfo: null
        });
      }
      // 启动Feed自动刷新定时器，直接请求官方Feed
      if (this.feedPollingTimer) clearInterval(this.feedPollingTimer);
      if (typeof this.fetchOfficialFeed === 'function') {
        this.fetchOfficialFeed();
        this.feedPollingTimer = setInterval(() => {
          this.fetchOfficialFeed();
        }, this.data.pollingInterval * 1000);
      } else if (typeof this.__proto__.fetchOfficialFeed === 'function') {
        this.__proto__.fetchOfficialFeed.call(this);
        this.feedPollingTimer = setInterval(() => {
          this.__proto__.fetchOfficialFeed.call(this);
        }, this.data.pollingInterval * 1000);
      }
    },
    show: function () {
      // 页面激活时检查冷却状态
      this.updateButtonStatus();
      this._setupShareCooldownTimer();

      // 页面显示时暂停自动刷新，避免影响用户体验
      if (this.feedPollingTimer) {
        clearInterval(this.feedPollingTimer);
        this.feedPollingTimer = null;
        console.log('[Feed] 页面显示，暂停自动刷新');
      }

      // 检查是否从后台返回且处于生成状态
      if (this.data.generating) {
        console.log('页面从后台返回，生成状态为true，重置状态并清理WebSocket连接');

        // 清理可能的WebSocket连接
        if (this.data.wsService) {
          console.log('[texttoimage] onShow: Found wsService, attempting disconnect.');
          this.data.wsService.close(); // 使用原生 close 方法
          this.setData({ wsService: null }); // 清空引用
        }

        // 重置生成相关的UI状态
        this.setData({
          generating: false,
          progress: 0,
          progressText: '生成中断，请重试',
          queuePosition: null,
          totalQueued: null
        });

        // 弹出对话框询问是否查看结果
        wx.showModal({
          title: '提示',
          content: '图像生成任务可能已在后台完成，是否查看结果？',
          confirmText: '查看图像生成结果',
          success: (res) => {
            if (res.confirm) {
              wx.navigateTo({
                url: '/pages/about/ai/mygenerateImage/mygenerateImage'
              });
            }
          }
        });
      }
    },
    detached: function () {
      // 优化事件解绑和清理
      this._unbindEvents();
      // eventBus.off('tabBarCollapseChange', this.handleTabBarChange.bind(this));
      // 清理冷却定时器
      if (this.data.shareCooldownTimer) {
        clearTimeout(this.data.shareCooldownTimer);
        this.setData({ shareCooldownTimer: null });
      }
      // 统一清理所有timer
      if (this._buttonCooldownTimers) {
        Object.values(this._buttonCooldownTimers).forEach(timer => {
          if (typeof timer === 'number') clearInterval(timer);
        });
      }
      // 移除登录状态更新监听
      // eventBus.off('loginStatusUpdate', this.handleLoginStatusUpdate.bind(this));

      // 新增：页面卸载时关闭 WebSocket 连接
      if (this.data.wsService) {
        console.log('[texttoimage] Detached: Closing WebSocket connection.');
        this.data.wsService.close(); // 使用原生 close 方法
        this.setData({ wsService: null }); // 清空引用
      }
      // 清理Feed自动刷新定时器
      if (this.feedPollingTimer) {
        clearInterval(this.feedPollingTimer);
        this.feedPollingTimer = null;
      }
      // 清理Feed重试定时器
      if (this.data.feedRetryTimer) {
        clearTimeout(this.data.feedRetryTimer);
        this.setData({ feedRetryTimer: null });
      }
      // 清理Feed自动更新定时器
      this.stopAutoUpdate();
    }
  },

  methods: {
    // 优化事件绑定方法
    _bindEvents() {
      eventBus.on('tabBarCollapseChange', this.handleTabBarChange.bind(this));
    },

    // 优化事件解绑方法
    _unbindEvents() {
      eventBus.off('tabBarCollapseChange', this.handleTabBarChange.bind(this));
    },
    // 新增：Pollinations模型选择器改变事件
    onPollinationsModelChange: function (e: any) {
      const index = e.detail.value;
      this.setData({
        selectedPollinationsModelIndex: index
      });
    },

    // 使用通用函数处理TabBar变化
    handleTabBarChange: function (data: {
      isCollapsed: boolean,
      expandedHeight: number,
      collapsedHeight: number,
      currentHeight: number
    }) {
      handleTabBarChangeUtil(this, data);
    },

    // 新增：输入框输入事件，实时更新prompt
    onPromptInput: function (e: any) {
      this.setData({ prompt: e.detail.value });
    },

    // 新增：翻译成英文按钮点击
    handleTranslate: async function () {
      if (this.data.buttonCooldowns.translate.disabled) return;
      const prompt = this.data.prompt || '';
      if (!prompt) {
        showErrorMessage('请输入关键词');
        return;
      }
      if (prompt.trim()) {
        startButtonCooldown(this, 'translate', this.data.totalcontroltime.translation_button.interval || 10);
        wx.showLoading({ title: '翻译中...' });
        this.setData({ loading: true, isInteractingDisabled: true });
        try {
          const finalPrompt = await zhipu.translateChineseToEnglish({ content: prompt });
          if (finalPrompt) {
            this.setData({
              prompt: finalPrompt.translation ? finalPrompt.translation : prompt,
            });
          }
        } catch (e) {
          console.error('翻译失败', e);
        } finally {
          wx.hideLoading();
          this.setData({ loading: false, isInteractingDisabled: false });
        }
      }
    },

    // 新增：提示词扩充按钮点击
    handleExpandPrompt: async function () {
      if (this.data.buttonCooldowns.expand.disabled) return;
      const prompt = this.data.prompt || '';
      if (!prompt) {
        showErrorMessage('请输入关键词');
        return;
      }
      if (prompt.trim()) {
        startButtonCooldown(this, 'expand', this.data.totalcontroltime.prompt_enhance_button.interval || 10);
        wx.showLoading({ title: '扩充关键词中...' });
        this.setData({ loading: true, isInteractingDisabled: true });
        try {
          const finalPrompt = await zhipu.expandDrawingPrompt({ content: prompt });
          if (finalPrompt) {
            this.setData({
              prompt: finalPrompt.expanded_prompt ? finalPrompt.expanded_prompt : finalPrompt,
            });
          }
        } catch (e) {
          console.error('扩充失败', e);
        } finally {
          wx.hideLoading();
          this.setData({ loading: false, isInteractingDisabled: false });
        }
      }
    },
    /**
       * Handles clearing the prompt input.
       */
    clearPrompt() {
      this.setData({
        prompt: ''
      });
    },
    // 新增：选择图片比例事件
    selectImageSize: function (e: any) {
      const index = e.currentTarget.dataset.index;
      this.setData({ selectedSizeIndex: index });
      const sizes = this.data.imageSizes || [];
      if (sizes.length > 0 && sizes[index]) {
        const size = sizes[index];
        // 假设size对象有width和height字段
        // console.log('选中图片比例:', size.label, '宽:', size.width,'高:',  size.height, size.value);
      } else {
        console.warn('图片比例数据异常');
      }
    },

    // 新增：选择画面风格事件
    selectImageStyle: function (e: any) {
      const index = e.currentTarget.dataset.index;
      this.setData({ selectedStyleIndex: index });
      const styles = this.data.imageStyles || [];
      if (styles.length > 0 && styles[index]) {
        const style = styles[index];
        // console.log('选中画面风格:', style.label,':',  style.value);
      } else {
        console.warn('画面风格数据异常');
      }
    },

    // 新增：生成图片事件
    generateImage: async function () {
      this.setData({ scrollTop: 0 }); // 每次生成前滚动到顶部
      let { prompt, imageSizes, imageStyles, selectedSizeIndex, selectedStyleIndex, keepSubject, lastSeed } = this.data;
      if (!prompt) {
        showErrorMessage('请输入关键词');
        return;
      }
      
      // 检查登录状态
      if (!this.data.isLogin && prompt) {
        console.log('用户未登录，触发登录弹窗');
        eventBus.emit('loginModalEvent', {
          show: true,
          source: 'texttoimage',
          callback: (userInfo) => {
            this.setData({
              isLogin: true,
              userInfo: userInfo
            });
            if (userInfo) {
              // 登录成功，继续生成图片
              console.log('登录成功，继续生成图片');
              // 收集当前页面的生成参数
              const currentParams = {
                prompt: this.data.prompt,
                imageSizes: this.data.imageSizes,
                imageStyles: this.data.imageStyles,
                selectedSizeIndex: this.data.selectedSizeIndex,
                selectedStyleIndex: this.data.selectedStyleIndex,
                keepSubject: this.data.keepSubject,
                lastSeed: this.data.lastSeed
              };
              this._continueGenerateImage(currentParams);
            } else {
              // 用户取消或登录失败
              console.log('登录取消或失败');
              this.setData({ generating: false, isInteractingDisabled: false }); // 停止生成中的状态
              wx.hideLoading();
            }
          }
        });
        this.setData({ generating: false, isInteractingDisabled: false }); // 触发登录弹窗后立即停止生成中的状态
        wx.hideLoading();
        return; // 暂停当前 generateImage 执行
      }

      // 如果已登录，直接继续执行生成逻辑
      console.log('用户已登录，直接生成图片');
      const currentParams = {
         prompt: this.data.prompt,
         imageSizes: this.data.imageSizes,
         imageStyles: this.data.imageStyles,
         selectedSizeIndex: this.data.selectedSizeIndex,
         selectedStyleIndex: this.data.selectedStyleIndex,
         keepSubject: this.data.keepSubject,
         lastSeed: this.data.lastSeed
       };
      this._continueGenerateImage(currentParams);
    },

    // 新增：继续生成图片的方法（登录成功后调用）
    _continueGenerateImage: async function(params: {
      prompt: string;
      imageSizes: any[];
      imageStyles: any[];
      selectedSizeIndex: number;
      selectedStyleIndex: number;
      keepSubject: boolean;
      lastSeed: number | null;
    }) {
       this.setData({ scrollTop: 0 }); // 每次生成前滚动到顶部
       let { prompt, imageSizes, imageStyles, selectedSizeIndex, selectedStyleIndex, keepSubject, lastSeed } = params; // 从参数中获取
       const size = (imageSizes && imageSizes[selectedSizeIndex]) || null;
       const style = (imageStyles && imageStyles[selectedStyleIndex]) || null;

       // 新增：在建立新连接前关闭旧连接
       if (this.data.wsService) {
         console.log('[texttoimage] Found existing WebSocket connection, closing before creating new one.');
         this.data.wsService.close(); // 使用原生 close 方法
         this.setData({ wsService: null }); // 清空引用
       }

       // 检查关键词合规性（保留，因为_continueGenerateImage也可能直接调用）
       const checkViolationKeywords = await zhipu.checkViolationKeywords({ content: prompt });
       if (checkViolationKeywords.has_violation) {
         const violation_keywords = checkViolationKeywords.violation_keywords.join(',');
         showErrorMessage('请输入合规的关键词\n避免出现'+violation_keywords+'等敏感内容');
         this.setData({ generating: false, isInteractingDisabled: false });
         wx.hideLoading();
         return;
       }

       wx.showLoading({ title: '准备生成...' });
       this.setData({ generating: true, generatedImageResult: null, isInteractingDisabled: true }); // 生成开始时禁用交互
       // 随机生成processing提示
       const hints = PROCESSING_HINTS;
       const randomProcessingHint = hints[Math.floor(Math.random() * hints.length)];
       this.setData({ progress: 0, progressText: randomProcessingHint }); // 新任务开始时重置进度
       updateProgressUtil(this, 5, '提交生成请求');
       this.setData({ queuePosition: null, totalQueued: null });

       try {
         // 翻译prompt和style
         let styleValue = null;
         let promptEn = prompt ? await zhipu.translateChineseToEnglish({ content: prompt }) : null;
         promptEn = promptEn.translation + ',flux';
         if (style && style.value) {
           styleValue = await zhipu.translateChineseToEnglish({ content: style.value });
           promptEn = promptEn + ',' + styleValue.translation;
         }
         // seed逻辑
         let seed = null;
         if (keepSubject) {
           if (lastSeed !== null && lastSeed !== undefined) {
             seed = lastSeed;
           } else {
             seed = Math.floor(Math.random() * 100000);
             this.setData({ lastSeed: seed }); // 注意：这里更新的是页面的 lastSeed
           }
         } else {
           seed = Math.floor(Math.random() * 100000);
           this.setData({ lastSeed: seed }); // 每次生成新的 seed 时也更新页面状态
         }

         // ===== WebSocket生图逻辑开始 =====
         const wsUrl = 'wss://tool.feifan919.com/ws'; // TODO: 替换为实际WebSocket服务地址
         const wsService = wx.connectSocket({ url: wsUrl }); // 使用原生API创建SocketTask
         this.setData({ wsService: wsService }); // 将新创建的实例存入 data

         // 随机选取一条PROCESSING_HINTS
         const hints = PROCESSING_HINTS;

         // 监听WebSocket原生事件
         wsService.onOpen(() => {
           console.log('[WebSocket] 连接已打开');
           this.setData({ webSocketConnected: true });
           // 连接打开后发送生成消息
           wsService.send({
             data: JSON.stringify({
               action: 'generate_image',
               data: {
                 prompt: promptEn,
                 width: size.width,
                 height: size.height,
                 enhance: true,
                 nologo: true,
                 seed: seed,
                 model: this.data.pollinationsModels[this.data.selectedPollinationsModelIndex],
                 user_id: this.data.userInfo ? this.data.userInfo.user_id : null
               }
             })
           });
         });

         wsService.onMessage((res) => {
           console.log('[WebSocket] 收到服务器消息:', res.data);
           try {
             const message = typeof res.data === 'string' ? JSON.parse(res.data) : res.data; // 解析消息

             // 根据 action 字段判断消息类型
             switch (message.action) {
               case 'task_created':
                 console.log('[WebSocket] 任务已创建:', message.data.task_id, message.data.queue_position, message.data.total_queued);
                 this.setData({ webSocketTaskId: message.data.task_id, queuePosition: message.data.queue_position, totalQueued: message.data.total_queued });
                 // 更新进度和提示语到排队状态
                 const queuedHints = QUEUED_HINTS; // 使用定义的排队提示语
                 const randomQueuedHint = queuedHints[Math.floor(Math.random() * queuedHints.length)];
                 this.setData({ progress: QUEUED_PROGRESS_START + (message.data.queue_position / (message.data.total_queued || 1)) * (QUEUED_PROGRESS_END - QUEUED_PROGRESS_START), progressText: randomQueuedHint });
                 updateProgressUtil(this, this.data.progress, this.data.progressText);
                 break;

               case 'task_status':
                 console.log('[WebSocket] 任务状态更新:', message.data.task_id, message.data.status);
                 // 根据 status 字段处理不同状态
                 switch (message.data.status) {
                   case 'processing':
                     // 更新进度和提示语到处理中状态
                     const processingHints = PROCESSING_HINTS;
                     const randomProcessingHint = processingHints[Math.floor(Math.random() * processingHints.length)];
                     // 可以在这里根据 progress 字段（如果服务器提供）更新进度，或者使用固定区间
                     this.setData({ progress: PROCESSING_PROGRESS_START + (message.data.progress || 0) * (PROCESSING_PROGRESS_END - PROCESSING_PROGRESS_START), progressText: randomProcessingHint });
                     updateProgressUtil(this, this.data.progress, this.data.progressText);
                      // 同时更新排队信息（如果消息中包含）
                     if (typeof message.data.queue_position !== 'undefined' && typeof message.data.total_queued !== 'undefined') {
                       this.setData({ queuePosition: message.data.queue_position, totalQueued: message.data.total_queued });
                     }
                     break;

                   case 'completed':
                     console.log('[WebSocket] 收到图片结果:', message.data.task_id);
                     // 解析 result 字段（这是一个 JSON 字符串）
                     let resultData = null;
                     try {
                       resultData = JSON.parse(message.data.result);
                     } catch (parseError) {
                       console.error('解析图片结果JSON字符串失败', parseError);
                       showErrorMessage('收到无效的图片结果格式');
                       this.setData({ generating: false, isInteractingDisabled: false, progress: 0, progressText: '结果错误', webSocketConnected: false });
                       wx.hideLoading();
                       return;
                     }

                     // 从解析后的结果中提取图片信息
                     let finalImageResult = null;

                     // 优先处理 image_base64
                     if (resultData && resultData.image_base64) {
                       console.log('[WebSocket] 收到 Base64 图片数据');
                       finalImageResult = {
                         image_base64: resultData.image_base64,
                         imagePath: 'data:image/png;base64,' + resultData.image_base64, // 格式化为数据 URL
                         remoteUrl: '' // 清空 remoteUrl
                       };
                     } else if (resultData && (resultData.imageUrl || resultData.imagePath)) {
                       // 如果没有 Base64，再处理远程图片 URL
                       console.log('[WebSocket] 收到远程图片 URL');
                       const remoteImageUrl = resultData.imageUrl || resultData.imagePath;
                       finalImageResult = {
                         image_base64: '', // 清空 base64
                         imagePath: remoteImageUrl, // 使用远程 URL
                         remoteUrl: remoteImageUrl // 使用远程 URL
                       };
                     } else {
                       // 没有找到图片信息
                       console.error('图片结果中未包含图片URL或Base64', resultData);
                       showErrorMessage('生成成功但未收到图片数据');
                       this.setData({ generating: false, isInteractingDisabled: false, progress: 0, progressText: '无图片数据', webSocketConnected: false });
                       wx.hideLoading();
                       return;
                     }

                     this.setData({
                       generatedImageResult: finalImageResult,
                       progress: 100,
                       progressText: '图片生成完成',
                       queuePosition: null,
                       totalQueued: null
                     });
                     updateProgressUtil(this, 100, '图片生成完成');
                     this.increaseGenerateCount();
                     this.setData({ generating: false, isInteractingDisabled: false });
                     wx.hideLoading();
                     // 新增：生成成功后触发冷却
                     startButtonCooldown(this, 'generate', this.data.generationCooldownInterval);
                     break;

                   case 'failed': // 假设服务器会发送 status: 'failed' 表示生成失败
                     showErrorMessage('图像生成失败：' + (message.data.reason || '未知原因'));
                     this.setData({ generating: false, isInteractingDisabled: false, progress: 0, progressText: '生成失败', webSocketConnected: false });
                     wx.hideLoading();
                     break;

                   // 可以添加其他状态的处理，如 'cancelled', 'pending' 等
                   default:
                     console.log('[WebSocket] 未知任务状态:', message.data.status);
                     // 对于未知状态，可以只记录日志或根据需要更新提示
                      const defaultProcessingHint = PROCESSING_HINTS[Math.floor(Math.random() * PROCESSING_HINTS.length)];
                      this.setData({ progressText: `状态: ${message.data.status} - ${defaultProcessingHint}` });
                      updateProgressUtil(this, this.data.progress, this.data.progressText);
                     break;
                 }
                 break;

               case 'error': // 假设服务器会发送 action: 'error'
                 showErrorMessage('服务器错误：' + (message.data.msg || '未知错误'));
                 this.setData({ generating: false, isInteractingDisabled: false, progress: 0, progressText: '生成失败', webSocketConnected: false });
                 wx.hideLoading();
                 break;

               default:
                 console.log('[WebSocket] 收到未知 action:', message.action, message);
                 // 对于未知 action，只记录日志
                 break;
             }

           } catch (e) {
             console.error('解析WebSocket消息失败', e);
             showErrorMessage('接收到无效的服务器消息');
             this.setData({ generating: false, isInteractingDisabled: false, progress: 0, progressText: '消息错误', webSocketConnected: false });
             wx.hideLoading();
           }
         });

         wsService.onError((err) => {
           console.error('[WebSocket] 连接错误:', err);
           showErrorMessage('WebSocket连接错误：' + (err && err.errMsg ? err.errMsg : '未知错误'));
           this.setData({ generating: false, isInteractingDisabled: false, progress: 0, progressText: '连接错误', webSocketConnected: false });
           wx.hideLoading();
         });

         wsService.onClose((res) => {
           console.log('[WebSocket] 连接已关闭:', res.code, res.reason);
           // 只有在当前页面正在生成时，连接非正常关闭才需要提示
           if (this.data.generating) {
             showErrorMessage('WebSocket连接关闭：' + (res.reason || '原因未知'));
             this.setData({ generating: false, isInteractingDisabled: false, progress: 0, progressText: '连接关闭', webSocketConnected: false });
             wx.hideLoading();
           } else {
              // 如果不是在生成状态下关闭，可能是主动关闭，无需提示
             this.setData({ webSocketConnected: false });
           }
         });

       } catch (error) {
         console.error('生成图片异常', error);
         showErrorMessage('图片生成过程中发生错误');
         this.setData({ generatedImageResult: null, progress: 0, progressText: '生成失败' });
         this.setData({ queuePosition: null, totalQueued: null });
         this.setData({ generating: false, isInteractingDisabled: false }); // 出现错误时也解除禁用和生成状态
         wx.hideLoading();
       } finally {
       }
    },

    // 新增：保存图片到相册事件
    saveImage: async function () {
      const imageBase64 = this.data.generatedImageResult && this.data.generatedImageResult.image_base64;
      const imagePath = this.data.generatedImageResult && this.data.generatedImageResult.imagePath;
      if (!imageBase64 && !imagePath) {
        showErrorMessage('没有可保存的图片');
        return;
      }
      wx.showLoading({ title: '保存中...' });
      try {
        if (imageBase64) {  
          // 将Base64转换为临时文件路径
          const fs = wx.getFileSystemManager();
          const filePath = `${wx.env.USER_DATA_PATH}/temp_image_${Date.now()}.png`;
          fs.writeFileSync(filePath, imageBase64, 'base64');
        // 调用保存图片工具函数
        await saveImageToAlbum(filePath);
        wx.showToast({ title: '保存成功', icon: 'success' });
        // 保存成功后可以考虑删除临时文件，尽管小程序有自己的管理机制
        // fs.unlinkSync(filePath);
        } else if (imagePath) {
          await saveImageToAlbum(imagePath);
          wx.showToast({ title: '保存成功', icon: 'success' });
        }
      } catch (e: any) {
        console.error('保存图片失败', e);
        // 检查是否是权限问题
        if (e.errMsg && e.errMsg.includes('permission denied')) {
          wx.showModal({
            title: '授权提示',
            content: '保存图片到相册需要您的授权，请点击确定并在设置页中开启相册权限',
            showCancel: true,
            success(res) {
              if (res.confirm) {
                wx.openSetting({
                  success(settingRes) {
                    console.log('设置页返回', settingRes)
                  }
                })
              }
            }
          })
        } else {
          showErrorMessage('保存失败，请稍后重试');
        }

      } finally {
        wx.hideLoading();
      }
    },

    // 新增：预览图片事件
    previewImage: function (e: any) {
      const imageBase64 = this.data.generatedImageResult && this.data.generatedImageResult.image_base64;
      const imageUrl = e.currentTarget && e.currentTarget.dataset && e.currentTarget.dataset.url; // 尝试从历史记录中获取url
      const imagePath = this.data.generatedImageResult && this.data.generatedImageResult.imagePath;

      if (!imageBase64 && !imageUrl && !imagePath) {
        showErrorMessage('没有可预览的图片');
        return;
      }

      let urls: string[] = [];
      let current = '';

      if (imageUrl) {
        // 如果是点击历史记录中的图片预览
        urls = this.data.generatedImages.map((item: { remoteUrl: any; }) => item.remoteUrl).filter(Boolean);
        current = imageUrl;
      } else if (imageBase64) {
        // 如果是点击生成结果的预览按钮或图片本身
        // 将Base64转换为临时文件路径用于预览
        try {
          const fs = wx.getFileSystemManager();
          const filePath = `${wx.env.USER_DATA_PATH}/temp_preview_${Date.now()}.png`;
          fs.writeFileSync(filePath, imageBase64, 'base64');
          urls = [filePath]; // 预览API接受一个URL数组
          current = filePath;
          // 可以在预览关闭后删除临时文件
          // wx.onAfterTapPreviewImage(() => { fs.unlinkSync(filePath); }); // 小程序暂无此API，需手动处理或依赖小程序回收
        } catch (e) {
          console.error('转换Base64到临时文件失败', e);
          showErrorMessage('预览图片失败');
          return;
        }
      } else if (imagePath) {
        urls = [imagePath];
        current = imagePath;
      }

      if (urls.length > 0) {
        wx.previewImage({
          urls: urls,
          current: current,
          success: function () { },
          fail: function (e) {
            console.error('预览失败', e);
            showErrorMessage('预览失败');
          }
        });
      }
    },


    // 新增：保持主体开关变更事件
    onKeepSubjectChange: function (e: any) {
      this.setData({ keepSubject: e.detail.value });
    },

    // 新增：生成设置展开/收起事件
    toggleSettings: function () {
      this.setData({ showSettings: !this.data.showSettings });
    },
    // 新增：处理图片加载错误
    handleImageError: function (e: any) {
      console.error('图片加载失败', e);
      this.setData({ ImageError: true });
      // 可以显示一个占位图或者提示信息
    },

    /**
     * 分页状态更新
     */
    updatePaginationStatus() {
      const { feedTotal, feedPageSize, feedPage, feedLoading } = this.data;
      const totalPages = feedPageSize > 0 ? Math.ceil(feedTotal / feedPageSize) : 1;
      this.setData({
        totalPages,
        isPrevDisabled: feedPage <= 1 || feedLoading,
        isNextDisabled: feedPage >= totalPages || feedLoading
      });
    },

    /**
     * 更新当前页feed数据
     */
    updateCurrentFeedPageList() {
      const { feedList, feedPage, feedPageSize } = this.data;
      const start = (feedPage - 1) * feedPageSize;
      let currentFeedPageList = feedList.slice(start, start + feedPageSize); // 使用let以便修改
      // console.log('currentFeedPageList',currentFeedPageList);

      // 检查并处理过长图片URL
      const MAX_URL_LENGTH = 15000; // 设置URL长度阈值
      const PLACEHOLDER_IMAGE = '/assets/placeholder.png'; // 占位符图片路径

      currentFeedPageList = currentFeedPageList.map(item => {
        if (item.imageURL && item.imageURL.length > MAX_URL_LENGTH) {
          return {
            ...item,
            displayImageURL: PLACEHOLDER_IMAGE // 使用占位符图片显示
          };
        }
        return item;
      });

      // 重置图片加载状态
      const feedImageLoaded = new Array(currentFeedPageList.length).fill(false);
      this.setData({ currentFeedPageList, feedImageLoaded }, () => {
        this.updatePaginationStatus();
      });
    },

    /**
     * 图片加载成功
     */
    onFeedImageLoad(e) {
      const index = e.currentTarget.dataset.index;
      const arr = this.data.feedImageLoaded.slice();
      arr[index] = true;
      this.setData({ feedImageLoaded: arr });
    },

    /**
     * 图片加载失败
     */
    onFeedImageError(e) {
      const index = e.currentTarget.dataset.index;
      const arr = this.data.feedImageLoaded.slice();
      arr[index] = false;
      this.setData({ feedImageLoaded: arr });
    },

    /**
     * 直接请求官方Feed接口，获取图片Feed
     * 支持SSE流式数据处理
     */
    fetchOfficialFeed: function () {
      this.setData({ feedLoading: true });
      console.log('[Feed] 请求 https://image.pollinations.ai/feed ...');

      // 直接使用SSE方式获取数据
      this.fetchFeedWithSSE().then((feedArr: FeedItem[]) => {
        // 检查是否获取到内容
        if (!feedArr || feedArr.length === 0) {
          console.warn('[Feed] 未获取到任何内容，1分钟后重试...');
          this.scheduleRetryFeed();
        } else {
          console.log('[Feed] 初次加载成功获取到', feedArr.length, '条数据');
          // 启动简单的定时更新：每10秒获取一张新图片
          this.startSimpleAutoUpdate();
        }
      }).catch((error: any) => {
        console.error('[Feed] 获取失败:', error);
        console.warn('[Feed] 获取失败，1分钟后重试...');
        this.scheduleRetryFeed();
      });
    },

    /**
     * 启动简单的自动更新：每10秒获取1条最新数据
     */
    startSimpleAutoUpdate: function() {
      // 停止之前的自动更新
      this.stopAutoUpdate();

      console.log('[Feed] 🚀 启动自动更新：每10秒获取1条最新数据');
      this.setData({ isAutoUpdating: true });

      // 定义递归更新函数
      const scheduleNextUpdate = () => {
        if (!this.data.isAutoUpdating) {
          console.log('[Feed] 自动更新已停止');
          return;
        }

        const now = new Date().toLocaleTimeString();
        console.log(`[Feed] ⏰ ${now} - 10秒延迟结束，开始获取1条最新数据...`);

        this.fetchSimpleSingleItem(() => {
          // 获取完成后，安排下一次10秒后的更新
          if (this.data.isAutoUpdating) {
            const timer = setTimeout(scheduleNextUpdate, this.data.feedAutoUpdateInterval);
            this.setData({ feedAutoUpdateTimer: timer });
          }
        });
      };

      // 10秒后开始第一次更新
      console.log('[Feed] 将在10秒后开始第一次自动更新...');
      const timer = setTimeout(scheduleNextUpdate, this.data.feedAutoUpdateInterval);
      this.setData({ feedAutoUpdateTimer: timer });
    },

    /**
     * 简单获取单条数据（带回调）
     */
    fetchSimpleSingleItem: function(callback?: () => void) {
      const that = this;
      let buffer = '';
      let foundItem = false;
      let completed = false;

      const complete = () => {
        if (completed) return;
        completed = true;
        if (callback) callback();
      };

      const reqTask = wx.request({
        url: 'https://image.pollinations.ai/feed',
        method: 'GET',
        enableChunked: true,
        responseType: 'arraybuffer',
        timeout: 8000,
        header: {
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache'
        },
        success(res) {
          if (res.statusCode !== 200) {
            console.warn(`[Feed] ❌ 请求失败，状态码: ${res.statusCode}`);
          } else {
            console.log('[Feed] 请求完成');
          }
          complete();
        },
        fail(err) {
          console.warn(`[Feed] ❌ 请求失败:`, err);
          complete();
        }
      });

      // 处理流式数据
      if (reqTask && typeof reqTask.onChunkReceived === 'function') {
        reqTask.onChunkReceived(function(res) {
          if (foundItem || completed) return;

          try {
            const result = that.processSSEDataWithBuffer(res.data, buffer);
            buffer = result.buffer;

            if (result.newItems && result.newItems.length > 0) {
              foundItem = true;
              const newItem = result.newItems[0];

              // 直接添加到页面最前面
              that.addNewFeedItems([newItem]);

              const now = new Date().toLocaleTimeString();
              console.log(`[Feed] ✅ ${now} - 成功获取并添加1条数据到最前面: ${newItem.prompt?.substring(0, 40)}...`);

              // 立即停止请求
              if (reqTask.abort) {
                reqTask.abort();
              }
              complete();
            }
          } catch (e) {
            console.warn('[Feed] 处理数据失败:', e);
          }
        });
      }
    },



    /**
     * 停止自动更新定时器
     */
    stopAutoUpdate: function() {
      this.setData({ isAutoUpdating: false });

      if (this.data.feedAutoUpdateTimer) {
        clearInterval(this.data.feedAutoUpdateTimer); // 改为clearInterval
        this.setData({ feedAutoUpdateTimer: null });
      }

      console.log('[Feed] 已停止自动更新');
    },



    /**
     * 安排延迟重试获取Feed
     */
    scheduleRetryFeed: function() {
      // 清除之前的重试定时器
      if (this.data.feedRetryTimer) {
        clearTimeout(this.data.feedRetryTimer);
      }

      this.setData({
        feedLoading: false
      });

      // 1分钟后重试
      const retryTimer = setTimeout(() => {
        console.log('[Feed] 开始重试获取...');
        this.setData({ feedRetryTimer: null });
        this.fetchOfficialFeed();
      }, 60000); // 60秒 = 1分钟

      this.setData({ feedRetryTimer: retryTimer });
      console.log('[Feed] 已安排1分钟后重试');
    },

    /**
     * 取消Feed重试
     */
    cancelFeedRetry: function() {
      if (this.data.feedRetryTimer) {
        clearTimeout(this.data.feedRetryTimer);
        this.setData({ feedRetryTimer: null });
        console.log('[Feed] 已取消重试');
      }
    },

    /**
     * 使用SSE方式获取Feed数据
     */
    fetchFeedWithSSE: function(): Promise<FeedItem[]> {
      const that = this;
      return new Promise((resolve, reject) => {
        let buffer = '';
        let feedArr: FeedItem[] = [];
        const maxInitialItems = 10; // 初始只获取10条数据
        let hasEnoughItems = false;

        const reqTask = wx.request({
          url: 'https://image.pollinations.ai/feed',
          method: 'GET',
          enableChunked: true,
          responseType: 'arraybuffer',
          timeout: 15000, // 缩短超时时间
          header: {
            'Accept': 'text/event-stream',
            'Cache-Control': 'no-cache'
          },
          success(res) {
            console.log('[Feed] 初始SSE请求完成，状态码:', res.statusCode);
            if (res.statusCode !== 200) {
              reject(new Error(`HTTP ${res.statusCode}`));
              return;
            }

            // 设置加载完成状态
            that.setData({ feedLoading: false });
            console.log('[Feed] 初始加载完成，获取到', feedArr.length, '条数据');
            resolve(feedArr);
          },
          fail(err) {
            console.error('[Feed] 初始SSE请求失败:', err);
            reject(err);
          }
        });

        // 处理流式数据 - 只获取初始数据，不实时更新
        if (reqTask && typeof reqTask.onChunkReceived === 'function') {
          reqTask.onChunkReceived(function(res) {
            if (hasEnoughItems) return; // 已获取足够数据，停止处理

            try {
              const result = that.processSSEDataWithBuffer(res.data, buffer);
              buffer = result.buffer;

              if (result.newItems && result.newItems.length > 0) {
                // 只添加到数组，不实时更新页面
                feedArr.push(...result.newItems);

                // 检查是否已获取足够数据
                if (feedArr.length >= maxInitialItems) {
                  hasEnoughItems = true;
                  console.log(`[Feed] 已获取${feedArr.length}条初始数据，停止接收`);

                  // 一次性更新页面
                  that.updateFeedData(feedArr);

                  // 停止请求
                  if (reqTask.abort) {
                    reqTask.abort();
                  }
                }
              }
            } catch (e) {
              console.warn('[Feed] 处理初始数据失败:', e);
            }
          });
        }
      });
    },



    /**
     * 处理SSE数据，返回新的buffer状态和解析出的新数据
     */
    processSSEDataWithBuffer: function(data: any, buffer: string): { buffer: string, newItems: FeedItem[] } {
      let text = '';
      try {
        if (data instanceof ArrayBuffer) {
          text = decodeURIComponent(escape(String.fromCharCode.apply(null, new Uint8Array(data))));
        } else {
          text = String(data);
        }
      } catch (e) {
        console.warn('[Feed] 数据解码失败:', e);
        return { buffer, newItems: [] };
      }

      buffer += text;
      const lines = buffer.split('\n');
      let remain = '';
      const newItems: FeedItem[] = [];

      for (let i = 0; i < lines.length; i++) {
        let line = lines[i].trim();
        if (line.startsWith('data:')) {
          let jsonStr = line.replace(/^data:\s*/, '');

          // 跳过空的data行
          if (!jsonStr || jsonStr.trim() === '') {
            continue;
          }

          try {
            const obj = JSON.parse(jsonStr);
            if (obj && obj.imageURL) {
              const newItem: FeedItem = {
                imageURL: obj.imageURL,
                model: obj.model,
                width: obj.width,
                height: obj.height,
                seed: obj.seed,
                enhance: obj.enhance,
                status: obj.status,
                prompt: obj.prompt
              };
              newItems.push(newItem);
              console.log('[Feed] 成功解析一条数据:', obj.prompt?.substring(0, 50) + '...');
            }
          } catch (e) {
            console.warn('[Feed] JSON解析失败，长度:', jsonStr.length, '前100字符:', jsonStr.substring(0, 100));
          }
        } else if (i === lines.length - 1) {
          remain = line;
        }
      }

      return { buffer: remain, newItems };
    },

    /**
     * 处理SSE数据（兼容旧接口）
     */
    processSSEData: function(data: any, buffer: string, feedArr: FeedItem[]): FeedItem[] {
      const result = this.processSSEDataWithBuffer(data, buffer);
      if (result.newItems && result.newItems.length > 0) {
        feedArr.push(...result.newItems);
      }
      return result.newItems;
    },

    /**
     * 解析Feed数据
     */
    parseFeedData: function(data: any): FeedItem[] {
      const feedArr: FeedItem[] = [];
      try {
        if (typeof data === 'string') {
          // 处理SSE格式的数据
          const lines = data.split('\n');
          for (const line of lines) {
            if (line.trim().startsWith('data:')) {
              const jsonStr = line.replace(/^data:\s*/, '');
              try {
                const obj = JSON.parse(jsonStr);
                if (obj && obj.imageURL) {
                  feedArr.push({
                    imageURL: obj.imageURL,
                    model: obj.model,
                    width: obj.width,
                    height: obj.height,
                    seed: obj.seed,
                    enhance: obj.enhance,
                    status: obj.status,
                    prompt: obj.prompt
                  });
                }
              } catch (e) {
                console.warn('[Feed] JSON解析失败:', jsonStr);
              }
            }
          }
        } else if (Array.isArray(data)) {
          // 处理数组格式的数据
          data.forEach((item: any) => {
            if (item && item.imageURL) {
              feedArr.push({
                imageURL: item.imageURL,
                model: item.model,
                width: item.width,
                height: item.height,
                seed: item.seed,
                enhance: item.enhance,
                status: item.status,
                prompt: item.prompt
              });
            }
          });
        }
      } catch (e) {
        console.error('[Feed] 数据解析失败:', e);
      }
      return feedArr;
    },

    /**
     * 实时添加新的Feed数据到列表前面（滚动列表，最多50条）
     */
    addNewFeedItems: function(newItems: FeedItem[]) {
      if (!newItems || newItems.length === 0) return;

      const currentFeedList = this.data.feedList || [];
      const validNewItems = newItems.filter((item: FeedItem) => !!item.imageURL);

      // 避免重复添加相同的数据，使用更严格的检测
      const filteredNewItems = validNewItems.filter(newItem => {
        const isDuplicate = currentFeedList.some(existingItem => {
          // 检查URL是否相同
          if (existingItem.imageURL === newItem.imageURL) {
            return true;
          }
          // 检查prompt和seed是否相同（更严格的重复检测）
          if (existingItem.prompt === newItem.prompt &&
              existingItem.seed === newItem.seed &&
              existingItem.model === newItem.model) {
            return true;
          }
          return false;
        });

        if (isDuplicate) {
          console.log('[Feed] 跳过重复数据:', newItem.prompt?.substring(0, 30) + '...');
        }
        return !isDuplicate;
      });

      if (filteredNewItems.length === 0) {
        console.log('[Feed] 没有新的数据需要添加（全部为重复数据）');
        return;
      }

      // 将新数据添加到列表前面
      let updatedFeedList = [...filteredNewItems, ...currentFeedList];

      // 实现滚动列表：如果超过最大数量，移除末尾的数据
      const maxItems = this.data.maxFeedItems;
      let removedCount = 0;

      if (updatedFeedList.length > maxItems) {
        removedCount = updatedFeedList.length - maxItems;
        updatedFeedList = updatedFeedList.slice(0, maxItems);
        console.log(`[Feed] 🗑️ 列表已达到最大容量${maxItems}条，移除末尾${removedCount}条数据`);
      }

      // 智能更新当前页列表，避免整体刷新
      this.smartUpdateCurrentPageList(filteredNewItems, updatedFeedList);

      console.log(`[Feed] ✅ 成功添加${filteredNewItems.length}条新数据到列表最前面`);
      console.log(`[Feed] 📊 当前列表状态: 总数${updatedFeedList.length}条，最大${maxItems}条，共${Math.ceil(updatedFeedList.length / this.data.feedPageSize)}页`);
      console.log(`[Feed] 📋 新数据: ${filteredNewItems.map(item => item.prompt?.substring(0, 20) + '...').join(', ')}`);
    },

    /**
     * 智能更新当前页列表，平滑插入新数据到最前面
     */
    smartUpdateCurrentPageList: function(newItems: FeedItem[], updatedFeedList: FeedItem[]) {
      const { feedPage, feedPageSize, currentFeedPageList, feedImageLoaded } = this.data;

      // 如果当前不在第一页，只更新总数据，不更新当前页显示
      if (feedPage !== 1) {
        this.setData({
          feedList: updatedFeedList,
          feedTotal: updatedFeedList.length,
          feedLoading: false
        }, () => {
          this.updatePaginationStatus();
        });
        console.log('[Feed] 📄 不在第一页，只更新总数据');
        return;
      }

      // 如果在第一页，平滑插入新数据到最前面
      const processedNewItems = this.processItemsForDisplay(newItems);

      // 计算需要保留的原有数据数量
      const maxItemsToKeep = feedPageSize - processedNewItems.length;
      const keptOldItems = currentFeedPageList.slice(0, Math.max(0, maxItemsToKeep));

      // 新的当前页列表：新数据在前，原有数据在后
      const newCurrentPageList = [...processedNewItems, ...keptOldItems];

      // 智能处理图片加载状态：新数据为false，原有数据保持状态
      const newFeedImageLoaded = new Array(newCurrentPageList.length).fill(false);

      // 为新数据设置加载状态为false（需要重新加载）
      for (let i = 0; i < processedNewItems.length; i++) {
        newFeedImageLoaded[i] = false;
      }

      // 保留原有数据的加载状态
      for (let i = processedNewItems.length; i < newCurrentPageList.length; i++) {
        const oldIndex = i - processedNewItems.length;
        if (oldIndex < feedImageLoaded.length) {
          newFeedImageLoaded[i] = feedImageLoaded[oldIndex];
        }
      }

      // 更新数据
      this.setData({
        feedList: updatedFeedList,
        feedTotal: updatedFeedList.length,
        currentFeedPageList: newCurrentPageList,
        feedImageLoaded: newFeedImageLoaded,
        feedLoading: false
      }, () => {
        this.updatePaginationStatus();
      });

      console.log(`[Feed] 📱 平滑插入${processedNewItems.length}条新数据到第一页最前面，当前页显示${newCurrentPageList.length}条`);
    },

    /**
     * 处理显示项目（检查URL长度等）
     */
    processItemsForDisplay: function(items: FeedItem[]): FeedItem[] {
      const MAX_URL_LENGTH = 15000;
      const PLACEHOLDER_IMAGE = '/assets/placeholder.png';

      return items.map(item => {
        if (item.imageURL && item.imageURL.length > MAX_URL_LENGTH) {
          return {
            ...item,
            displayImageURL: PLACEHOLDER_IMAGE
          };
        }
        return item;
      });
    },

    /**
     * 更新Feed数据到页面
     */
    updateFeedData: function(feedArr: FeedItem[]) {
      const validFeedArr = feedArr.filter((item: FeedItem) => !!item.imageURL);
      this.setData({
        feedList: validFeedArr,
        feedTotal: validFeedArr.length,
        feedPage: 1,
        feedLoading: false
      }, () => {
        this.updateCurrentFeedPageList();
        this.updatePaginationStatus();
      });
    },
    /**
     * 获取当前页feed数据
     */
    getCurrentFeedPage() {
      const { feedList, feedPage, feedPageSize } = this.data;
      const start = (feedPage - 1) * feedPageSize;
      return feedList.slice(start, start + feedPageSize);
    },
    /**
     * 上一页
     */
    loadPrevFeedPage() {
      if (this.data.feedPage > 1) {
        this.setData({ feedPage: this.data.feedPage - 1 }, () => {
          this.updateCurrentFeedPageList();
          this.updatePaginationStatus();
        });
      }
    },
    /**
     * 下一页
     */
    loadNextFeedPage() {
      const maxPage = Math.ceil(this.data.feedTotal / this.data.feedPageSize);
      if (this.data.feedPage < maxPage) {
        this.setData({ feedPage: this.data.feedPage + 1 }, () => {
          this.updateCurrentFeedPageList();
          this.updatePaginationStatus();
        });
      }
    },
    /**
     * 刷新feed，强制请求远程并更新缓存
     */
    refreshFeed() {
      console.log('[Feed] 手动刷新feed');
      // 停止自动更新
      this.stopAutoUpdate();
      // 取消之前的重试
      this.cancelFeedRetry();
      // 重新获取初始数据
      this.fetchOfficialFeed();
    },

    /**
     * 手动刷新feed按钮点击事件
     */
    onRefreshFeedTap() {
      if (this.data.feedLoading) {
        wx.showToast({
          title: '正在加载中...',
          icon: 'loading',
          duration: 1000
        });
        return;
      }
      this.refreshFeed();
      wx.showToast({
        title: '正在刷新...',
        icon: 'loading',
        duration: 1000
      });
    },
    /**
     * 显示feed图片弹窗
     */
    showFeedImageModal(e) {
      const index = e.currentTarget.dataset.index;
      const imgData = this.data.currentFeedPageList[index];
      this.setData({
        showFeedImageModal: true,
        feedImageModalData: imgData,
        showDoSameBtn: imgData.prompt && imgData.prompt.length <= 200
      });
    },

    /**
     * 关闭feed图片弹窗
     */
    closeFeedImageModal() {
      this.setData({ showFeedImageModal: false, feedImageModalData: null });
    },

    /**
     * 做同款：将弹窗图像的prompt和seed填入主页面
     */
    doSameFeedImage: async function () {
      this.setData({
        prompt: '',
      });
      const prompt = this.data.prompt || '';
      if (!prompt) {
        wx.showLoading({ title: '读取中...' });
      }
      const data = this.data.feedImageModalData;
      let translatecn = { translation: '' };
      try {
        
        translatecn = await zhipu.translate({ content: data.prompt || '', target_lang: '中文' });
        if (data) {
          this.setData({
            prompt: translatecn.translation ? translatecn.translation : data.prompt,
            lastSeed: data.seed,
            showFeedImageModal: false,
            feedImageModalData: null
          });
          this.setData({ scrollTop: 0 });
        }
      } catch (e) {
        console.error('翻译失败', e);
        // 保证translatecn为对象，避免类型错误
        translatecn = { translation: '' };
      } finally {

        wx.hideLoading();
        this.setData({ loading: false, isInteractingDisabled: false }); // 生成结束时启用交互
      }

    },
    // ====== 新增：生成图片/分享按钮显示状态更新 ======
    updateButtonStatus: function (generateCount, shareNumber, lastShareTime) {
      // 若未传入参数则取data
      if (typeof generateCount === 'undefined') generateCount = this.data.generateCount;
      if (typeof shareNumber === 'undefined') shareNumber = this.data.currentShareNumber; // 用动态上限
      if (typeof lastShareTime === 'undefined') lastShareTime = this.data.lastShareTime;
      const now = Math.floor(Date.now() / 1000);
      const cooldown = this.data.shareCountdownTime || 10;
      // 判断是否在冷却期
      if (lastShareTime && now - lastShareTime < cooldown) {
        this.setData({ showGenerateBtn: false, showShareBtn: true });
        return;
      }
      this.setData({ shareBtnText: '分享给好友继续使用' }); // 显示生成按钮时重置
      if (generateCount < shareNumber) {
        this.setData({ showGenerateBtn: true, showShareBtn: false });
      } else {
        this.setData({ showGenerateBtn: false, showShareBtn: true });
      }
    },
    // ====== 新增：设置冷却定时器 ======
    _setupShareCooldownTimer: function () {
      // 清理旧定时器
      if (this.data.shareCooldownTimer) {
        clearTimeout(this.data.shareCooldownTimer);
        this.setData({ shareCooldownTimer: null });
      }
      const lastShareTime = this.data.lastShareTime;
      const cooldown = this.data.shareCountdownTime || 10;
      if (!lastShareTime) return;
      const now = Math.floor(Date.now() / 1000);
      let remain = cooldown - (now - lastShareTime);
      if (remain > 0) {
        // 每秒打印一次剩余秒数
        const printInterval = setInterval(() => {
          remain--;
          if (remain > 0) {
            console.log('[texttoimage] 分享冷却剩余秒数:', remain);
          } else {
            clearInterval(printInterval);
          }
        }, 1000);
        // 首次打印
        console.log('[texttoimage] 分享冷却剩余秒数:', remain);
        const timer = setTimeout(() => {
          clearInterval(printInterval);
          this.setData({ shareBtnText: '分享给好友继续使用' }); // 冷却结束重置按钮文字
          this.updateButtonStatus();
          this.setData({ shareCooldownTimer: null });
        }, remain * 1000);
        this.setData({ shareCooldownTimer: timer });
      }
    },
    // ====== 新增：分享按钮事件处理 ======
    handleShare: function () {
      this.checkAndResetShareData();
      const now = Math.floor(Date.now() / 1000);
      const cooldown = this.data.shareCountdownTime || 10;
      const lastShareTime = this.data.lastShareTime || 0;
      // 冷却期内分享
      if (lastShareTime && now - lastShareTime < cooldown) {
        wx.showShareMenu({ withShareTicket: true });
        this.setData({ shareBtnText: '请分享到不同的群/好友' });
        return;
      }
      // 非冷却期分享
      if (typeof wx.showShareMenu === 'function') {
        wx.showShareMenu({ withShareTicket: true });
      }
      // 记录本次分享时间戳
      let shareTimes = this.data.shareTimes + 1;
      let currentShareNumber = this.data.shareNumber * (shareTimes + 0); // 第一次分享后为2倍
      const newShareTime = now;
      this.setData({
        generateCount: 0,
        lastShareTime: newShareTime,
        shareBtnText: '分享给好友继续使用',
        shareTimes,
        currentShareNumber
      });
      try {
        wx.setStorageSync(this.data.generateCountStorageKey, 0);
        wx.setStorageSync(this.data.lastShareTimeStorageKey, newShareTime);
        wx.setStorageSync(this.data.shareTimesStorageKey, shareTimes);
        wx.setStorageSync(this.data.currentShareNumberStorageKey, currentShareNumber);
        wx.setStorageSync(this.data.shareDateStorageKey, this._getTodayStr());
      } catch (e) { }
      this.updateButtonStatus(0, currentShareNumber, newShareTime);
      this._setupShareCooldownTimer();
    },
    // ====== 新增：页面分享回调 ======
    onShareAppMessage: function () {
      // 分享内容可自定义
      return {
        title: 'AI文生图，快来体验！',
        path: '/pages/ai/texttoimage/texttoimage',
        success: () => {
          // 分享成功后重置计数
          this.setData({ generateCount: 0 });
          try {
            wx.setStorageSync(this.data.generateCountStorageKey, 0);
          } catch (e) { }
          this.updateButtonStatus(0, this.data.shareNumber, this.data.lastShareTime);
        },
        fail: () => {
          // 分享失败不做处理
        }
      };
    },
    // ====== 新增：生成次数+1并持久化 ======
    increaseGenerateCount: function () {
      this.checkAndResetShareData();
      let generateCount = this.data.generateCount + 1;
      this.setData({ generateCount });
      try {
        wx.setStorageSync(this.data.generateCountStorageKey, generateCount);
      } catch (e) { }
      this.updateButtonStatus(generateCount, this.data.currentShareNumber, this.data.lastShareTime);
    },
    // ====== 新增：日期校验与重置 ======
    _getTodayStr: function () {
      const d = new Date();
      const y = d.getFullYear();
      const m = (d.getMonth() + 1).toString().padStart(2, '0');
      const day = d.getDate().toString().padStart(2, '0');
      return `${y}${m}${day}`;
    },
    checkAndResetShareData: function () {
      const today = this._getTodayStr();
      let { shareDate, currentShareNumber, shareTimes, shareNumber } = this.data;
      if (!shareDate || shareDate !== today) {
        currentShareNumber = shareNumber;
        shareTimes = 0;
        shareDate = today;
        wx.setStorageSync(this.data.currentShareNumberStorageKey, currentShareNumber);
        wx.setStorageSync(this.data.shareTimesStorageKey, shareTimes);
        wx.setStorageSync(this.data.shareDateStorageKey, shareDate);
        this.setData({ currentShareNumber, shareTimes, shareDate });
      }
    },
    toggleIncreaseTip: function () {
      this.setData({ showIncreaseTip: !this.data.showIncreaseTip });
    },
    /**
     * 随机填充高质量flux模型prompt
     */
    handleRandomPrompt: function (): void {
      const randomIndex = Math.floor(Math.random() * fluxPrompts.length);
      const randomPrompt = fluxPrompts[randomIndex];
      this.setData({ prompt: randomPrompt });
    },
    // 新增：登录状态更新处理
    handleLoginStatusUpdate: function (data: { isLogin: boolean, userInfo?: any }) {
      console.log('[texttoimage] loginStatusUpdate received:', data);
      this.setData({ isLogin: data.isLogin, userInfo: data.userInfo || null });
    },
    goToMyAssets: function () {
      wx.navigateTo({
        url: '/pages/about/ai/mygenerateImage/mygenerateImage'
      });
    },
  },
});
